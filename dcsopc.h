#ifndef DCSOPC_H
#define DCSOPC_H

#include <string>
#include <mutex>
#include <vector>
#include <unordered_map>
#include <chrono>
#include "config_manager.h"

#ifdef _WIN32
#include <windows.h>
#include <oleauto.h>
// 包含DACLTSDK头文件
#include "DACLTSDK.h"

// 32位程序稳定性：安全的VARIANT管理类
class SafeVariant {
private:
    VARIANT m_variant;
    bool m_initialized;

public:
    SafeVariant() : m_initialized(false) {
        try {
            VariantInit(&m_variant);
            m_initialized = true;
        } catch (...) {
            m_initialized = false;
        }
    }

    ~SafeVariant() {
        clear();
    }

    void clear() {
        if (m_initialized) {
            try {
                VariantClear(&m_variant);
                VariantInit(&m_variant);
            } catch (...) {
                // 忽略清理异常
            }
        }
    }

    VARIANT* get() { return m_initialized ? &m_variant : nullptr; }
    const VARIANT* get() const { return m_initialized ? &m_variant : nullptr; }

    bool isValid() const { return m_initialized; }

    // 禁用拷贝构造和赋值操作，避免VARIANT重复释放
    SafeVariant(const SafeVariant&) = delete;
    SafeVariant& operator=(const SafeVariant&) = delete;
};
#endif



/**
 * @brief DCS OPC通信设备类
 * 
 * 该类实现通过OPC协议与DCS系统进行通信，
 * 提供与DCSDevice相同的接口，便于系统集成
 */
class DCSOPCDevice {
public:
    DCSOPCDevice(ConfigManager *config_manager, std::string dcs_name);
    ~DCSOPCDevice();
    
    // 加载配置
    int load_config();
    
    // OPC连接管理
    int connect_opc_server();
    int disconnect_opc_server();
    bool is_opc_connected() const { return opc_connected; }

    // OPC服务器发现功能
    int discover_opc_servers();
    int try_connect_with_discovery();
    
    // 数据采集
    int read_data();
    
    // 启动采集线程
    void start_data_collect();
    void start_raw_material_collect();  // 保留接口兼容性，但内部不再启动独立线程

    // 线程函数
    void do_data_collect();             // 统一采集函数：同时采集主要参数和生料量数据
    void do_raw_material_collect();     // 保留接口兼容性，但不再使用

    // OPC数据读写（使用DACLTSDK）
    int read_opc_item(const std::string& tag_name, VARIANT& value);
    int write_opc_item(const std::string& tag_name, const VARIANT& value);
    
    // 写入单个参数到DCS
    int write_float_value(const std::string& tag_name, float value);

    // 配置项
    ConfigManager *config_manager;
    std::string dcs_name;
    std::string desc;
    std::string protocol;
    int collection_interval;                // 统一采集间隔
    int raw_material_collection_interval;  // 保留配置兼容性，但使用collection_interval
    std::string associated_boiler;
    
    // OPC配置
    std::string opc_server_prog_id;
    std::string opc_server_host;
    DWORD opc_update_rate;
    std::string opc_group_name;

    // OPC服务器发现配置
    bool opc_auto_discovery;
    std::string opc_server_class_id;
    std::vector<std::string> opc_server_prog_id_list;
    
    // OPC标签配置
    std::string opc_furnace_set_temp_tag;            // 炉膛设定温度（DCS待新建）
    std::string opc_furnace_actual_temp_tag;
    std::string opc_actual_furnace_pressure_tag;
    std::string opc_coal_feed_rate_tag;
    std::string opc_actual_raw_material1_tag;        // 实际生料量1
    std::string opc_actual_raw_material2_tag;        // 实际生料量2
    std::string opc_planned_raw_material_tag;        // 计划生料量（DCS待新建）
    std::string opc_induced_draft_fan_speed_tag;

    // 新增四个设定值标签
    std::string opc_coal_feed_set_tag;               // 给煤量设定读取标签
    std::string opc_raw_material1_set_tag;           // 生料量1设定读取标签
    std::string opc_raw_material2_set_tag;           // 生料量2设定读取标签
    std::string opc_induced_draft_fan_set_tag;       // 引风机转速设定读取标签
    
    // OPC设定值写入标签配置（用于参数自动调整功能）
    std::string opc_coal_feed_set_write_tag;         // 给煤量设定写入标签
    std::string opc_raw_material1_set_write_tag;     // 生料量1设定写入标签
    std::string opc_raw_material2_set_write_tag;     // 生料量2设定写入标签
    std::string opc_induced_draft_fan_set_write_tag; // 引风机转速设定写入标签

    // 数据值 - 分解炉参数（与DCSDevice保持一致）
    float furnace_set_temp;       // 炉膛设定温度（DCS待新建标签）
    float furnace_actual_temp;    // 炉膛实际温度
    float actual_furnace_pressure; // 实际炉压
    float coal_feed_rate;         // 给煤量
    float actual_raw_material1;   // 实际生料量1
    float actual_raw_material2;   // 实际生料量2
    float planned_raw_material;   // 计划生料量（DCS待新建标签）
    float induced_draft_fan_speed; // 引风机转速

    // 新增四个设定值数据
    float coal_feed_set;          // 给煤量设定值
    float raw_material1_set;      // 生料量1设定值
    float raw_material2_set;      // 生料量2设定值
    float induced_draft_fan_set;  // 引风机转速设定值

    // 生料量数据的独立时间戳和有效性标志
    std::chrono::steady_clock::time_point last_raw_material_update;
    bool raw_material_data_valid;

    // 参数调整相关配置和状态
    int adjustment_interval_count;
    float raw_material_diff_threshold;
    unsigned short planned_raw_material_write_address;
    std::chrono::steady_clock::time_point last_adjustment_time;
    
    // 初始化状态标志
    bool is_initialized;

    // 重连控制相关
    std::chrono::steady_clock::time_point last_connect_attempt;
    int connect_retry_count;
    static const int MAX_RETRY_COUNT;           // 最大重连次数
    static const int RETRY_INTERVAL_SECONDS;   // 重连间隔（秒）

    // 互斥锁
    std::mutex rwMutex;

private:
#ifdef _WIN32
    // DACLTSDK相关句柄
    DWORD opc_server_handle;
    DWORD opc_group_handle;

    // OPC连接状态
    bool opc_connected;
    bool sdk_initialized;
    bool com_initialized;                       // COM环境初始化状态（32位程序内存优化）

    // OPC项句柄映射（标签名 -> 项句柄）
    std::unordered_map<std::string, DWORD> opc_item_handles;

    // 辅助函数
    int create_opc_group();
    int add_opc_items();
    void cleanup_opc_resources();
    std::string wstring_to_string(const std::wstring& wstr);

    // OPC服务器发现辅助函数
    std::vector<std::pair<std::string, std::string>> enumerate_opc_servers_on_host(const std::string& host);
    int try_connect_to_server(const std::string& server_name, const std::string& server_class_id);
#endif
};

// 外部变量声明
extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

// 函数声明
std::unordered_map<std::string, DCSOPCDevice*> get_dcs_opc_list(ConfigManager *config_manager);

// 新的数据获取接口（支持双生料量）
void get_realtime_dcs_opc_data_v2(std::string dcs_name,
                                 float *furnace_actual_temp,
                                 float *actual_furnace_pressure,
                                 float *coal_feed_rate,
                                 float *actual_raw_material1,
                                 float *actual_raw_material2,
                                 float *induced_draft_fan_speed);

// 新增设定值数据获取接口
void get_realtime_dcs_opc_setpoint_data(std::string dcs_name,
                                       float *coal_feed_set,
                                       float *raw_material1_set,
                                       float *raw_material2_set,
                                       float *induced_draft_fan_set);

// 兼容性接口（保持原有接口不变）
void get_realtime_dcs_opc_data(std::string dcs_name,
                              float *furnace_set_temp,
                              float *furnace_actual_temp,
                              float *actual_furnace_pressure,
                              float *coal_feed_rate,
                              float *actual_raw_material,
                              float *planned_raw_material,
                              float *induced_draft_fan_speed);

#endif // DCSOPC_H
