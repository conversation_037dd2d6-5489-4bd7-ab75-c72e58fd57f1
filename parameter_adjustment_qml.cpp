#include "parameter_adjustment_qml.h"
#include "parameter_adjustment.h"
#include "dcsopc.h"
#include <QVariantMap>
#include <QDebug>
#include <unordered_map>
#include <mutex>

// 声明在data.cpp中定义的调试函数
extern void debug_printf(const char* format, ...);

ParameterAdjustmentQML::ParameterAdjustmentQML(QObject *parent)
    : QObject(parent)
{
    debug_printf("参数调整QML包装类初始化\n");
}

void ParameterAdjustmentQML::enableAutoAdjustment(bool enable)
{
    debug_printf("QML接口: 设置自动调整模式 %s\n", enable ? "启用" : "禁用");
    enable_auto_parameter_adjustment(enable);
    emit autoAdjustmentStatusChanged(enable);
}

bool ParameterAdjustmentQML::getAdjustmentSuggestion(const QString& dcsName, 
                                                   float* suggestedValue, 
                                                   float* currentDiff,
                                                   QString* reason)
{
    if (!suggestedValue || !currentDiff || !reason) {
        debug_printf("QML接口错误: 空指针参数\n");
        return false;
    }
    
    char reason_buffer[256];
    bool needAdjustment = get_adjustment_suggestion(dcsName.toStdString().c_str(), 
                                                  suggestedValue, 
                                                  currentDiff,
                                                  reason_buffer, 
                                                  sizeof(reason_buffer));
    
    *reason = QString::fromUtf8(reason_buffer);
    
    debug_printf("QML接口: 获取调整建议 - DCS: %s, 需要调整: %s, 建议值: %.2f, 差值: %.2f\n",
                dcsName.toStdString().c_str(), needAdjustment ? "是" : "否", *suggestedValue, *currentDiff);
    
    return needAdjustment;
}

bool ParameterAdjustmentQML::executeManualAdjustment(const QString& dcsName, float newValue)
{
    debug_printf("QML接口: 手动调整模式 - DCS: %s, 新值: %.2f (仅提供建议，不自动执行)\n", dcsName.toStdString().c_str(), newValue);

    // 手动模式：只提供建议值，不自动执行DCS写入
    // 用户需要在DCS系统中手动调整参数
    bool success = true; // 手动模式总是返回成功，因为只是提供建议

    emit adjustmentExecuted(dcsName, newValue, success);

    return success;
}

bool ParameterAdjustmentQML::isAutoAdjustmentActive()
{
    return is_auto_adjustment_active();
}

QVariantMap ParameterAdjustmentQML::getCurrentAdjustmentInfo(const QString& dcsName)
{
    QVariantMap info;
    
    float suggestedValue = 0.0f;
    float currentDiff = 0.0f;
    QString reason;
    
    bool needAdjustment = getAdjustmentSuggestion(dcsName, &suggestedValue, &currentDiff, &reason);
    
    info["needAdjustment"] = needAdjustment;
    info["suggestedValue"] = suggestedValue;
    info["currentDiff"] = currentDiff;
    info["reason"] = reason;
    info["autoAdjustmentActive"] = isAutoAdjustmentActive();
    info["isAutoMode"] = isAutoAdjustmentActive();  // 为QML提供isAutoMode字段
    
    debug_printf("QML接口: 获取当前调整信息 - DCS: %s, 需要调整: %s\n", 
                dcsName.toStdString().c_str(), needAdjustment ? "是" : "否");
    
    return info;
}

QVariantMap ParameterAdjustmentQML::getCoalFeedAdjustmentInfo(const QString& dcsName)
{
    QVariantMap info;

    float suggestedValue = 0.0f;
    float currentValue = 0.0f;
    QString reason;

    char reason_buffer[256];
    bool needAdjustment = get_coal_feed_adjustment_suggestion(dcsName.toStdString().c_str(),
                                                            &suggestedValue,
                                                            &currentValue,
                                                            reason_buffer,
                                                            sizeof(reason_buffer));

    reason = QString::fromUtf8(reason_buffer);

    info["needAdjustment"] = needAdjustment;
    info["suggestedValue"] = suggestedValue;
    info["currentValue"] = currentValue;
    info["reason"] = reason;
    info["autoAdjustmentActive"] = isAutoAdjustmentActive();
    info["isAutoMode"] = isAutoAdjustmentActive();

    debug_printf("QML接口: 获取给煤量调整信息 - DCS: %s, 需要调整: %s\n",
                dcsName.toStdString().c_str(), needAdjustment ? "是" : "否");

    return info;
}



QVariantMap ParameterAdjustmentQML::getInducedDraftFanAdjustmentInfo(const QString& dcsName)
{
    QVariantMap info;

    float suggestedAdjustment = 0.0f;
    float currentPressure = 0.0f;
    QString reason;

    char reason_buffer[256];
    bool needAdjustment = get_induced_draft_fan_adjustment_suggestion(dcsName.toStdString().c_str(),
                                                                    &suggestedAdjustment,
                                                                    &currentPressure,
                                                                    reason_buffer,
                                                                    sizeof(reason_buffer));

    reason = QString::fromUtf8(reason_buffer);

    info["needAdjustment"] = needAdjustment;
    info["suggestedAdjustment"] = suggestedAdjustment;
    info["currentPressure"] = currentPressure;
    info["reason"] = reason;
    info["autoAdjustmentActive"] = isAutoAdjustmentActive();
    info["isAutoMode"] = isAutoAdjustmentActive();

    debug_printf("QML接口: 获取引风机调整信息 - DCS: %s, 需要调整: %s\n",
                dcsName.toStdString().c_str(), needAdjustment ? "是" : "否");

    return info;
}

void ParameterAdjustmentQML::toggleAutoAdjustment(bool enable)
{
    debug_printf("QML接口: 切换自动调整模式 %s\n", enable ? "启用" : "禁用");
    enableAutoAdjustment(enable);
}

bool ParameterAdjustmentQML::performManualAdjustment(const QString& dcsName, float newValue)
{
    debug_printf("QML接口: 手动调整模式 - DCS: %s, 新值: %.2f (仅提供建议)\n", dcsName.toStdString().c_str(), newValue);
    return executeManualAdjustment(dcsName, newValue);
}

void ParameterAdjustmentQML::setFurnacePressureSetpoint(float pressure)
{
    debug_printf("QML接口: 设置给定炉压 - 新值: %.1fPa\n", pressure);
    set_furnace_pressure_setpoint(pressure);
}

void ParameterAdjustmentQML::setOxygenConcentrationSetpoint(float concentration)
{
    debug_printf("QML接口: 设置设定氧气浓度 - 新值: %.2f%%\n", concentration);
    set_oxygen_concentration_setpoint(concentration);
}

float ParameterAdjustmentQML::getFurnacePressureSetpoint()
{
    float pressure = get_furnace_pressure_setpoint();
    debug_printf("QML接口: 获取给定炉压 - 当前值: %.1fPa\n", pressure);
    return pressure;
}

float ParameterAdjustmentQML::getOxygenConcentrationSetpoint()
{
    float concentration = get_oxygen_concentration_setpoint();
    debug_printf("QML接口: 获取设定氧气浓度 - 当前值: %.2f%%\n", concentration);
    return concentration;
}

void ParameterAdjustmentQML::setTempFurnaceSetTemp(float temp)
{
    debug_printf("QML接口: 设置临时炉膛设定温度 - 新值: %.1f℃\n", temp);
    set_temp_furnace_set_temp(temp);
}

void ParameterAdjustmentQML::setTempPlannedRawMaterial(float material)
{
    debug_printf("QML接口: 设置临时计划生料量 - 新值: %.1ft/h\n", material);
    set_temp_planned_raw_material(material);
}

float ParameterAdjustmentQML::getTempFurnaceSetTemp()
{
    float temp = get_temp_furnace_set_temp();
    debug_printf("QML接口: 获取临时炉膛设定温度 - 当前值: %.1f℃\n", temp);
    return temp;
}

float ParameterAdjustmentQML::getTempPlannedRawMaterial()
{
    float material = get_temp_planned_raw_material();
    debug_printf("QML接口: 获取临时计划生料量 - 当前值: %.1ft/h\n", material);
    return material;
}

QVariantMap ParameterAdjustmentQML::getCurrentDCSValues(const QString& dcsName)
{
    QVariantMap values;

    // 需要包含必要的头文件和外部声明
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    auto opc_it = dcs_opc_map.find(dcsName.toStdString());
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        DCSOPCDevice* dcs_opc_device = opc_it->second;

        // 加锁获取当前DCS参数值 - 只获取参数调整页面需要的3个参数
        {
            std::lock_guard<std::mutex> lock(dcs_opc_device->rwMutex);
            // 实际生料量 = 实际生料量1 + 实际生料量2
            // 注意：DCS数据单位已经是t/h，直接使用无需转换
            float raw_material1 = dcs_opc_device->actual_raw_material1;
            float raw_material2 = dcs_opc_device->actual_raw_material2;
            float total_raw_material = raw_material1 + raw_material2;

            values["actualRawMaterial"] = total_raw_material;
            values["actualRawMaterial1"] = raw_material1;  // 添加实际生料量1单独数据
            values["actualRawMaterial2"] = raw_material2;  // 添加实际生料量2单独数据
            values["plannedRawMaterial"] = dcs_opc_device->planned_raw_material;
            values["coalFeedRate"] = dcs_opc_device->coal_feed_rate;  // 给煤量已经是t/h单位，无需转换
            values["inducedDraftFanSpeed"] = dcs_opc_device->induced_draft_fan_speed;
        }

        debug_printf("QML接口: 获取当前DCS参数值 - DCS: %s - 给煤量: %.3ft/h, 实际生料量: %.1ft/h, 引风机转速: %.1frpm\n",
                    dcsName.toStdString().c_str(), values["coalFeedRate"].toFloat(), values["actualRawMaterial"].toFloat(), values["inducedDraftFanSpeed"].toFloat());
    } else {
        debug_printf("QML接口错误: 无法获取DCS OPC设备 '%s'\n", dcsName.toStdString().c_str());
        // 返回默认值 - 只返回参数调整页面需要的3个参数
        values["actualRawMaterial"] = 0.0f;
        values["coalFeedRate"] = 0.0f;
        values["inducedDraftFanSpeed"] = 0.0f;
    }

    return values;
}

QVariantMap ParameterAdjustmentQML::getCurrentAdjustmentSuggestion()
{
    QVariantMap suggestion;

    if (!g_parameter_adjustment_manager) {
        debug_printf("QML接口错误: 参数调整管理器未初始化\n");
        return suggestion;
    }

    auto current_suggestion = g_parameter_adjustment_manager->get_current_adjustment_suggestion();

    suggestion["hasSuggestion"] = current_suggestion.has_suggestion;
    suggestion["parameterName"] = QString::fromStdString(current_suggestion.parameter_name);
    suggestion["suggestedValue"] = current_suggestion.suggested_value;
    suggestion["currentValue"] = current_suggestion.current_value;
    suggestion["unit"] = QString::fromStdString(current_suggestion.unit);
    suggestion["reason"] = QString::fromStdString(current_suggestion.reason);

    debug_printf("QML接口: 获取当前调整建议 - 参数: %s, 建议值: %.3f%s\n",
                current_suggestion.parameter_name.c_str(),
                current_suggestion.suggested_value,
                current_suggestion.unit.c_str());

    return suggestion;
}

int ParameterAdjustmentQML::getDCSCollectionInterval(const QString& dcsName)
{
    // 需要包含必要的头文件和外部声明
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    auto opc_it = dcs_opc_map.find(dcsName.toStdString());
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        DCSOPCDevice* dcs_opc_device = opc_it->second;
        int collectionInterval = dcs_opc_device->collection_interval;

        debug_printf("QML接口: 获取DCS设备 '%s' 采集间隔 - %d秒\n",
                    dcsName.toStdString().c_str(), collectionInterval);

        return collectionInterval;
    } else {
        debug_printf("QML接口错误: 无法获取DCS OPC设备 '%s' 的采集间隔\n",
                    dcsName.toStdString().c_str());
        return 0;  // 返回0表示获取失败
    }
}

void ParameterAdjustmentQML::setRawMaterialSetpointTarget(int target)
{
    debug_printf("QML接口: 设置生料量设定目标 - %d (%s)\n", target,
                target == 1 ? "生料量1设定" : "生料量2设定");
    set_raw_material_setpoint_target(target);
}

int ParameterAdjustmentQML::getRawMaterialSetpointTarget()
{
    int target = get_raw_material_setpoint_target();
    debug_printf("QML接口: 获取生料量设定目标 - %d (%s)\n", target,
                target == 1 ? "生料量1设定" : "生料量2设定");
    return target;
}

QVariantMap ParameterAdjustmentQML::getCurrentDCSSetValues(const QString& dcsName)
{
    QVariantMap values;

    float coal_feed_set = 0.0f;
    float raw_material1_set = 0.0f;
    float raw_material2_set = 0.0f;
    float induced_draft_fan_set = 0.0f;

    // 调用C接口获取设定值数据
    get_current_dcs_set_values(dcsName.toStdString().c_str(),
                              &coal_feed_set, &raw_material1_set,
                              &raw_material2_set, &induced_draft_fan_set);

    // 注意：DCS设定值数据单位已经是t/h，直接使用无需转换
    values["coalFeedSet"] = coal_feed_set;
    values["rawMaterial1Set"] = raw_material1_set;
    values["rawMaterial2Set"] = raw_material2_set;
    values["inducedDraftFanSet"] = induced_draft_fan_set;

    debug_printf("QML接口: 获取DCS设备 '%s' 设定值数据 - 给煤量设定: %.1ft/h, 生料量1设定: %.1ft/h, 生料量2设定: %.1ft/h, 引风机转速设定: %.1frpm\n",
                dcsName.toStdString().c_str(), coal_feed_set, raw_material1_set, raw_material2_set, induced_draft_fan_set);

    return values;
}

QVariantMap ParameterAdjustmentQML::getRawMaterialSetSuggestion()
{
    QVariantMap suggestion;

    if (!g_parameter_adjustment_manager) {
        debug_printf("QML接口错误: 参数调整管理器未初始化\n");
        return suggestion;
    }

    auto raw_material_suggestion = g_parameter_adjustment_manager->get_raw_material_set_suggestion();

    suggestion["hasSuggestion"] = raw_material_suggestion.has_suggestion;
    suggestion["rawMaterial1SetSuggested"] = raw_material_suggestion.raw_material1_set_suggested;
    suggestion["rawMaterial1SetCurrent"] = raw_material_suggestion.raw_material1_set_current;
    suggestion["rawMaterial2SetSuggested"] = raw_material_suggestion.raw_material2_set_suggested;
    suggestion["rawMaterial2SetCurrent"] = raw_material_suggestion.raw_material2_set_current;
    suggestion["unit"] = QString::fromStdString(raw_material_suggestion.unit);
    suggestion["reason"] = QString::fromStdString(raw_material_suggestion.reason);

    debug_printf("QML接口: 获取生料量设定调整建议 - 生料量1设定: %.2f→%.2f%s, 生料量2设定: %.2f→%.2f%s\n",
                raw_material_suggestion.raw_material1_set_current, raw_material_suggestion.raw_material1_set_suggested, raw_material_suggestion.unit.c_str(),
                raw_material_suggestion.raw_material2_set_current, raw_material_suggestion.raw_material2_set_suggested, raw_material_suggestion.unit.c_str());

    return suggestion;
}
