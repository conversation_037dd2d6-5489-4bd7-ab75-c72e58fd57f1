import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12

Page {
    id: configPage

    // 信号定义
    signal navigateBack()

    // 本地状态管理
    property var rs485Config: ({})
    property var dcsOPCConfig: ({})
    property var parameterAdjustmentConfig: ({})
    property bool hasUnsavedChanges: false

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回监控"
                onClicked: {
                    if (hasUnsavedChanges) {
                        confirmDialog.open()
                    } else {
                        stackView.pop()
                    }
                }
            }
            Label {
                text: "采集配置管理"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
                color: "white"
            }

            Button {
                text: "重新加载"
                onClicked: {
                    loadConfigs()
                }
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: "保存配置"
                enabled: hasUnsavedChanges
                onClicked: {
                    saveConfigs()
                }
                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#388e3c" : "#4caf50") : "#666666"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }

    // 确认对话框
    Dialog {
        id: confirmDialog
        anchors.centerIn: parent
        width: 400
        height: 200
        title: "未保存的更改"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "您有未保存的配置更改，是否要保存？"
                font.pixelSize: 16
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }

            RowLayout {
                Layout.fillWidth: true

                Button {
                    text: "保存并返回"
                    Layout.fillWidth: true
                    onClicked: {
                        saveConfigs()
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "不保存"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#d32f2f" : "#f44336"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "取消"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 重启数据采集确认对话框
    Dialog {
        id: restartDialog
        anchors.centerIn: parent
        width: 500
        height: 250
        title: "配置保存成功"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "✅ 配置已成功保存到config.ini文件！"
                font.pixelSize: 16
                font.bold: true
                color: "#4caf50"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }

            Label {
                text: "为了使新的配置生效，需要重启应用程序。\n这将会：\n• 保存当前配置到文件\n• 使用重启脚本彻底关闭当前应用程序\n• 自动启动新的应用程序实例\n• 使用新配置重新初始化所有模块\n• 重新建立设备连接和数据采集\n\n是否现在重启应用程序？"
                font.pixelSize: 14
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
                color: "#333333"
            }

            RowLayout {
                Layout.fillWidth: true

                Button {
                    text: "立即重启应用"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        restartApplication()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "稍后手动重启"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        // 显示提示消息
                        successMessage.text = "配置已保存！请手动重启应用程序使配置生效。"
                        successMessage.visible = true
                        successTimer.start()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 主要内容区域
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // RS485配置区域（顶部，保持不变）
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: rs485Column.implicitHeight + 40
            color: "#ffffff"
            radius: 12
            border.color: "#e0e0e0"
            border.width: 1

            ColumnLayout {
                id: rs485Column
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15

                Label {
                    text: "RS485 烟气分析仪配置"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#333333"
                }

                GridLayout {
                    Layout.fillWidth: true
                    columns: 12
                    columnSpacing: 15
                    rowSpacing: 15

                    // 串口号
                    Label {
                        text: "串口号:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485PortCombo
                        Layout.preferredWidth: 100
                        editable: true
                        model: ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"]
                        currentIndex: {
                            var value = rs485Config.Port || "COM5"
                            var index = model.indexOf(value)
                            return index >= 0 ? index : -1
                        }
                        editText: rs485Config.Port || "COM5"
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.Port || "COM5")) {
                                hasUnsavedChanges = true
                            }
                        }
                        onEditTextChanged: {
                            if (editText !== (rs485Config.Port || "COM5")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 波特率
                    Label {
                        text: "波特率:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485BaudRateCombo
                        Layout.preferredWidth: 100
                        model: ["1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"]
                        currentIndex: {
                            var value = rs485Config.BaudRate || "9600"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.BaudRate || "9600")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 停止位
                    Label {
                        text: "停止位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485StopBitsCombo
                        Layout.preferredWidth: 100
                        model: ["1", "2"]
                        currentIndex: {
                            var value = rs485Config.StopBits || "1"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.StopBits || "1")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 校验位
                    Label {
                        text: "校验位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485ParityCombo
                        Layout.preferredWidth: 100
                        model: ["N", "E", "O"]
                        currentIndex: {
                            var value = rs485Config.Parity || "N"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.Parity || "N")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 数据位
                    Label {
                        text: "数据位:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    ComboBox {
                        id: rs485DataBitsCombo
                        Layout.preferredWidth: 100
                        model: ["7", "8"]
                        currentIndex: {
                            var value = rs485Config.DataBits || "8"
                            return model.indexOf(value)
                        }
                        onCurrentTextChanged: {
                            if (currentText !== (rs485Config.DataBits || "8")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 超时时间
                    Label {
                        text: "超时时间(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485TimeoutField
                        Layout.preferredWidth: 100
                        text: rs485Config.Timeout || ""
                        validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                        onTextChanged: {
                            if (text !== (rs485Config.Timeout || "")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 数据采集间隔
                    Label {
                        text: "数据采集间隔(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CollectionIntervalField
                        Layout.preferredWidth: 100
                        text: rs485Config.CollectionInterval || "15"
                        validator: IntValidator { bottom: 1; top: 300 }
                        onTextChanged: {
                            if (text !== (rs485Config.CollectionInterval || "15")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }



                    // 抽气泵电流传感器设备地址
                    Label {
                        text: "抽气泵电流地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CurrentField
                        Layout.preferredWidth: 100
                        text: rs485Config.Current || "60"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Current || "60")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }



                    // 压力表传感器设备地址
                    Label {
                        text: "压力表设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485VoltageField
                        Layout.preferredWidth: 100
                        text: rs485Config.Voltage || "50"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Voltage || "50")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // O2传感器设备地址
                    Label {
                        text: "O2设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485O2Field
                        Layout.preferredWidth: 100
                        text: rs485Config.O2 || "4"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.O2 || "4")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // CO传感器设备地址
                    Label {
                        text: "CO设备地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485CoField
                        Layout.preferredWidth: 100
                        text: rs485Config.Co || "1"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Co || "1")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 冷凝器温度传感器设备地址
                    Label {
                        text: "冷凝器温度地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485TempatureField
                        Layout.preferredWidth: 100
                        text: rs485Config.Tempature || "40"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Tempature || "40")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 开关量信号1设备地址
                    Label {
                        text: "开关信号1地址:"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485Switch1Field
                        Layout.preferredWidth: 100
                        text: rs485Config.Switch1 || "80"
                        validator: IntValidator { bottom: 1; top: 255 }
                        onTextChanged: {
                            if (text !== (rs485Config.Switch1 || "80")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }

                    // 反吹反馈停止后的延迟恢复时间
                    Label {
                        text: "反吹延迟时间(s):"
                        font.pixelSize: 14
                        color: "#666666"
                    }
                    TextField {
                        id: rs485BackflowDelayTimeField
                        Layout.preferredWidth: 100
                        text: rs485Config.BackflowDelayTime || "60"
                        validator: IntValidator { bottom: 1; top: 300 }
                        onTextChanged: {
                            if (text !== (rs485Config.BackflowDelayTime || "60")) {
                                hasUnsavedChanges = true
                            }
                        }
                    }
                }
            }
        }

        // 下方左右分栏区域
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 20

            // 左侧：DCS OPC配置区域（带独立滚动条）
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    Label {
                        text: "DCS OPC 采集和写入配置"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#195399"
                    }

                    ScrollView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        ColumnLayout {
                            width: parent.width
                            spacing: 20

                            // OPC服务器连接配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "OPC服务器连接配置"
                                font.pixelSize: 16
                                font.bold: true

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15

                                    // OPC服务器程序标识符
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "OPC服务器程序标识符:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcServerProgIDField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCServerProgID || "Matrikon.OPC.Simulation.1"
                                            placeholderText: "例如: Matrikon.OPC.Simulation.1"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCServerProgID || "Matrikon.OPC.Simulation.1")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // OPC服务器主机地址
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "OPC服务器主机地址:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcServerHostField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCServerHost || "localhost"
                                            placeholderText: "例如: localhost 或 *************"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCServerHost || "localhost")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // OPC组数据更新频率
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "OPC组数据更新频率(ms):"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcUpdateRateField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCUpdateRate || "1000"
                                            validator: IntValidator { bottom: 100; top: 10000 }
                                            placeholderText: "建议值: 500-2000ms"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCUpdateRate || "1000")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // DCS数据采集间隔
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "DCS数据采集间隔(s):"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: dcsCollectionIntervalField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.CollectionInterval || "20"
                                            validator: IntValidator { bottom: 1; top: 300 }
                                            placeholderText: "建议值: 10-60秒"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.CollectionInterval || "20")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // OPC组名称
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "OPC组名称:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcGroupNameField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCGroupName || "DCS_Group"
                                            placeholderText: "例如: DCS_Group"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCGroupName || "DCS_Group")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // OPC读取标签配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "OPC读取标签配置 - 数据采集标签"
                                font.pixelSize: 16
                                font.bold: true

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15

                                    // 炉膛设定温度读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "炉膛设定温度读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcFurnaceSetTempTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCFurnaceSetTempTag || ""
                                            placeholderText: "DCS待新建标签"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCFurnaceSetTempTag || "")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 炉膛实际温度读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "炉膛实际温度读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcFurnaceActualTempTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCFurnaceActualTempTag || "DCS.Furnace.ActualTemperature"
                                            placeholderText: "例如: DCS.Furnace.ActualTemperature"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCFurnaceActualTempTag || "DCS.Furnace.ActualTemperature")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 实际炉压读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "实际炉压读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcActualFurnacePressureTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCActualFurnacePressureTag || "DCS.Furnace.ActualPressure"
                                            placeholderText: "例如: DCS.Furnace.ActualPressure"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCActualFurnacePressureTag || "DCS.Furnace.ActualPressure")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 给煤量读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "给煤量读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcCoalFeedRateTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCCoalFeedRateTag || "DCS.Coal.FeedRate"
                                            placeholderText: "例如: DCS.Coal.FeedRate"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCCoalFeedRateTag || "DCS.Coal.FeedRate")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 实际生料量1读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "实际生料量1读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcActualRawMaterial1TagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCActualRawMaterial1Tag || "AI5202F01_R"
                                            placeholderText: "例如: AI5202F01_R"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCActualRawMaterial1Tag || "AI5202F01_R")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 实际生料量2读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "实际生料量2读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcActualRawMaterial2TagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCActualRawMaterial2Tag || "XSST_FH_R"
                                            placeholderText: "例如: XSST_FH_R"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCActualRawMaterial2Tag || "XSST_FH_R")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 计划生料量读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "计划生料量读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcPlannedRawMaterialTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCPlannedRawMaterialTag || ""
                                            placeholderText: "DCS待新建标签"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCPlannedRawMaterialTag || "")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 引风机转速读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "引风机转速读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcInducedDraftFanSpeedTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCInducedDraftFanSpeedTag || "DCS.Fan.InducedDraftSpeed"
                                            placeholderText: "例如: DCS.Fan.InducedDraftSpeed"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCInducedDraftFanSpeedTag || "DCS.Fan.InducedDraftSpeed")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 给煤量设定读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "给煤量设定读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcCoalFeedSetTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCCoalFeedSetTag || "761RS01_SG_M"
                                            placeholderText: "例如: 761RS01_SG_M"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCCoalFeedSetTag || "761RS01_SG_M")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 生料量1设定读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "生料量1设定读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcRawMaterial1SetTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCRawMaterial1SetTag || "AO5202F01_R"
                                            placeholderText: "例如: AO5202F01_R"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCRawMaterial1SetTag || "AO5202F01_R")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 生料量2设定读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "生料量2设定读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcRawMaterial2SetTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCRawMaterial2SetTag || "XSST_SG_R"
                                            placeholderText: "例如: XSST_SG_R"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCRawMaterial2SetTag || "XSST_SG_R")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 引风机转速设定读取标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "引风机转速设定读取标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcInducedDraftFanSetTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCInducedDraftFanSetTag || "AO5401S2_R1"
                                            placeholderText: "例如: AO5401S2_R1"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCInducedDraftFanSetTag || "AO5401S2_R1")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // OPC写入标签配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "OPC写入标签配置 - 参数调整控制标签"
                                font.pixelSize: 16
                                font.bold: true

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15

                                    // 给煤量设定写入标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "给煤量设定写入标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcCoalFeedSetWriteTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCCoalFeedSetWriteTag || "DCS.Coal.FeedSetWrite"
                                            placeholderText: "例如: DCS.Coal.FeedSetWrite"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCCoalFeedSetWriteTag || "DCS.Coal.FeedSetWrite")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 生料量1设定写入标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "生料量1设定写入标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcRawMaterial1SetWriteTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCRawMaterial1SetWriteTag || "DCS.RawMaterial1.SetWrite"
                                            placeholderText: "例如: DCS.RawMaterial1.SetWrite"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCRawMaterial1SetWriteTag || "DCS.RawMaterial1.SetWrite")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 生料量2设定写入标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "生料量2设定写入标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcRawMaterial2SetWriteTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCRawMaterial2SetWriteTag || "DCS.RawMaterial2.SetWrite"
                                            placeholderText: "例如: DCS.RawMaterial2.SetWrite"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCRawMaterial2SetWriteTag || "DCS.RawMaterial2.SetWrite")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                    // 引风机转速设定写入标签
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10

                                        Label {
                                            text: "引风机转速设定写入标签:"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.preferredWidth: 220
                                            Layout.alignment: Qt.AlignTop
                                        }
                                        TextField {
                                            id: opcInducedDraftFanSetWriteTagField
                                            Layout.fillWidth: true
                                            Layout.minimumWidth: 300
                                            Layout.preferredHeight: 35
                                            text: dcsOPCConfig.OPCInducedDraftFanSetWriteTag || "DCS.Fan.InducedDraftSetWrite"
                                            placeholderText: "例如: DCS.Fan.InducedDraftSetWrite"
                                            onTextChanged: {
                                                if (text !== (dcsOPCConfig.OPCInducedDraftFanSetWriteTag || "DCS.Fan.InducedDraftSetWrite")) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }

            // 右侧：参数调整配置区域（带独立滚动条）
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    Label {
                        text: "参数调整配置"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#333333"
                    }

                    ScrollView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        ColumnLayout {
                            width: parent.width
                            spacing: 20

                            // 调整模式配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "调整模式配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    Label {
                                        text: "自动调整模式:"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }

                                    RowLayout {
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        spacing: 10

                                        Switch {
                                            id: autoAdjustmentEnabledSwitch
                                            checked: parameterAdjustmentConfig.AutoAdjustmentEnabled === "true"
                                            onCheckedChanged: {
                                                var currentValue = parameterAdjustmentConfig.AutoAdjustmentEnabled === "true"
                                                if (checked !== currentValue) {
                                                    hasUnsavedChanges = true
                                                }
                                            }
                                        }
                                        Label {
                                            text: autoAdjustmentEnabledSwitch.checked ? "自动调整" : "手动调整"
                                            font.pixelSize: 14
                                            color: autoAdjustmentEnabledSwitch.checked ? "#4caf50" : "#ff9800"
                                            font.bold: true
                                        }
                                    }
                                }
                            }

                            // 调整间隔配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "调整间隔配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    // 参数调整检查间隔
                                    Label {
                                        text: "参数调整检查间隔:"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: adjustmentCheckIntervalField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.AdjustmentCheckInterval || "3"
                                        validator: IntValidator { bottom: 1; top: 20 }
                                        placeholderText: "每N次采集检查一次"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.AdjustmentCheckInterval || "3")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }
                                }
                            }

                            // 生料量调节配置
                            GroupBox {
                                Layout.fillWidth: true
                                title: "前置条件：生料量调节配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    // 生料量差值阈值
                                    Label {
                                        text: "生料量差值阈值(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: rawMaterialDiffThresholdField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.RawMaterialDiffThreshold || "10.0"
                                        validator: DoubleValidator { bottom: 0.1; top: 100.0; decimals: 1 }
                                        placeholderText: "|实际-计划| ≤ 阈值"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.RawMaterialDiffThreshold || "10.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 生料量调节步长
                                    Label {
                                        text: "生料量调节步长(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: rawMaterialAdjustmentStepField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.RawMaterialAdjustmentStep || "5.0"
                                        validator: DoubleValidator { bottom: 0.1; top: 50.0; decimals: 1 }
                                        placeholderText: "调节步长"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.RawMaterialAdjustmentStep || "5.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }
                                }
                            }

                            // 炉膛温度判断与给煤量调节配置
                            GroupBox {
                                Layout.fillWidth: true
                                // title: "步骤1：炉膛温度判断与给煤量调节配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    // 炉膛温度差值阈值
                                    Label {
                                        text: "炉膛温度差值阈值(℃):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnaceTempDiffThresholdField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnaceTempDiffThreshold || "10.0"
                                        validator: DoubleValidator { bottom: 1.0; top: 50.0; decimals: 1 }
                                        placeholderText: "|实际-设定| ≤ 阈值"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnaceTempDiffThreshold || "10.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 炉膛设定温度最大值
                                    Label {
                                        text: "炉膛设定温度最大值(℃):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnaceTempMaxValueField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnaceTempMaxValue || "1200.0"
                                        validator: DoubleValidator { bottom: 800.0; top: 1500.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnaceTempMaxValue || "1200.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 炉膛设定温度最小值
                                    Label {
                                        text: "炉膛设定温度最小值(℃):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnaceTempMinValueField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnaceTempMinValue || "800.0"
                                        validator: DoubleValidator { bottom: 500.0; top: 1000.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnaceTempMinValue || "800.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给煤量调节步长
                                    Label {
                                        text: "给煤量调节步长(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: coalFeedAdjustmentStepField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.CoalFeedAdjustmentStep || "10.0"
                                        validator: DoubleValidator { bottom: 1.0; top: 100.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.CoalFeedAdjustmentStep || "10.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给煤量最大值
                                    Label {
                                        text: "给煤量最大值(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: coalFeedMaxValueField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.CoalFeedMaxValue || "50000.0"
                                        validator: DoubleValidator { bottom: 1000.0; top: 100000.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.CoalFeedMaxValue || "50000.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给煤量最小值
                                    Label {
                                        text: "给煤量最小值(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: coalFeedMinValueField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.CoalFeedMinValue || "0.0"
                                        validator: DoubleValidator { bottom: 0.0; top: 1000.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.CoalFeedMinValue || "0.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }
                                }
                            }

                            // 氧气浓度判断与生料量/煤量调节配置
                            GroupBox {
                                Layout.fillWidth: true
                                // title: "步骤2：氧气浓度判断与生料量/煤量调节配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    // 氧气浓度差值阈值
                                    Label {
                                        text: "氧气浓度差值阈值(%):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: oxygenConcentrationDiffThresholdField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.OxygenConcentrationDiffThreshold || "0.1"
                                        validator: DoubleValidator { bottom: 0.01; top: 1.0; decimals: 2 }
                                        placeholderText: "|实际-设定| ≤ 阈值"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.OxygenConcentrationDiffThreshold || "0.1")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 设定氧气浓度
                                    Label {
                                        text: "设定氧气浓度(%):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: oxygenConcentrationSetpointField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.OxygenConcentrationSetpoint || "3.5"
                                        validator: DoubleValidator { bottom: 1.0; top: 10.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.OxygenConcentrationSetpoint || "3.5")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 氧气浓度调节生料量步长
                                    Label {
                                        text: "氧气浓度调节生料量步长(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: oxygenRawMaterialAdjustmentStepField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.OxygenRawMaterialAdjustmentStep || "3.0"
                                        validator: DoubleValidator { bottom: 0.1; top: 20.0; decimals: 1 }
                                        placeholderText: "浓度>设定时增加生料量"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.OxygenRawMaterialAdjustmentStep || "3.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 氧气浓度调节煤量步长
                                    Label {
                                        text: "氧气浓度调节煤量步长(kg):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: oxygenCoalFeedAdjustmentStepField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.OxygenCoalFeedAdjustmentStep || "5.0"
                                        validator: DoubleValidator { bottom: 0.1; top: 20.0; decimals: 1 }
                                        placeholderText: "浓度<设定时减少煤量"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.OxygenCoalFeedAdjustmentStep || "5.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }
                                }
                            }

                            // 炉压判断与引风机调节配置
                            GroupBox {
                                Layout.fillWidth: true
                                // title: "步骤3：炉压判断与引风机调节配置"
                                font.pixelSize: 16
                                font.bold: true

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 2
                                    columnSpacing: 20
                                    rowSpacing: 15

                                    // 炉压差值阈值
                                    Label {
                                        text: "炉压差值阈值(Pa):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnacePressureDiffThresholdField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnacePressureDiffThreshold || "1.0"
                                        validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                                        placeholderText: "|实际-给定| ≤ 阈值"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnacePressureDiffThreshold || "1.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给定炉压
                                    Label {
                                        text: "给定炉压(Pa):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnacePressureSetpointField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnacePressureSetpoint || "-75.0"
                                        validator: DoubleValidator { bottom: -150.0; top: 0.0; decimals: 1 }
                                        placeholderText: "范围-50至-100"
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnacePressureSetpoint || "-75.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给定炉压最小值
                                    Label {
                                        text: "给定炉压最小值(Pa):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnacePressureSetpointMinField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnacePressureSetpointMin || "-100.0"
                                        validator: DoubleValidator { bottom: -150.0; top: -50.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnacePressureSetpointMin || "-100.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 给定炉压最大值
                                    Label {
                                        text: "给定炉压最大值(Pa):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: furnacePressureSetpointMaxField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.FurnacePressureSetpointMax || "-50.0"
                                        validator: DoubleValidator { bottom: -100.0; top: 0.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.FurnacePressureSetpointMax || "-50.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 引风机转速调节步长
                                    Label {
                                        text: "引风机转速调节步长(rpm):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: inducedDraftFanAdjustmentStepField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.InducedDraftFanAdjustmentStep || "0.1"
                                        validator: DoubleValidator { bottom: 0.01; top: 1.0; decimals: 2 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.InducedDraftFanAdjustmentStep || "0.1")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 引风机最大转速
                                    Label {
                                        text: "引风机最大转速(rpm):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: inducedDraftFanMaxSpeedField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.InducedDraftFanMaxSpeed || "50.0"
                                        validator: DoubleValidator { bottom: 30.0; top: 100.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.InducedDraftFanMaxSpeed || "50.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }

                                    // 引风机最小转速
                                    Label {
                                        text: "引风机最小转速(rpm):"
                                        font.pixelSize: 14
                                        color: "#666666"
                                        Layout.fillWidth: true
                                    }
                                    TextField {
                                        id: inducedDraftFanMinSpeedField
                                        Layout.preferredWidth: 200
                                        Layout.alignment: Qt.AlignHCenter
                                        text: parameterAdjustmentConfig.InducedDraftFanMinSpeed || "10.0"
                                        validator: DoubleValidator { bottom: 5.0; top: 30.0; decimals: 1 }
                                        horizontalAlignment: Text.AlignHCenter
                                        onTextChanged: {
                                            if (text !== (parameterAdjustmentConfig.InducedDraftFanMinSpeed || "10.0")) {
                                                hasUnsavedChanges = true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 成功消息
    Rectangle {
        id: successMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#4caf50"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: successLabel
            anchors.centerIn: parent
            text: successMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 错误消息
    Rectangle {
        id: errorMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#f44336"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: errorLabel
            anchors.centerIn: parent
            text: errorMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 成功消息定时器
    Timer {
        id: successTimer
        interval: 3000
        onTriggered: successMessage.visible = false
    }

    // 错误消息定时器
    Timer {
        id: errorTimer
        interval: 3000
        onTriggered: errorMessage.visible = false
    }

    // 页面初始化时加载配置
    Component.onCompleted: {
        loadConfigs()
    }

    // 加载配置的函数
    function loadConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            rs485Config = configManagerQML.getRS485Config()
            dcsOPCConfig = configManagerQML.getDCSOPCConfig()
            parameterAdjustmentConfig = configManagerQML.getParameterAdjustmentConfig()
            hasUnsavedChanges = false
        }
    }

    // 保存配置的函数
    function saveConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            // 收集RS485通信配置（只包含串口通信参数）
            var newRS485Config = {
                "Port": rs485PortCombo.editable ? rs485PortCombo.editText : rs485PortCombo.currentText,
                "BaudRate": rs485BaudRateCombo.currentText,
                "StopBits": rs485StopBitsCombo.currentText,
                "Parity": rs485ParityCombo.currentText,
                "DataBits": rs485DataBitsCombo.currentText,
                "Timeout": rs485TimeoutField.text
            }

            // 收集烟气分析仪设备配置（设备地址和采集参数）
            var newSmokeAnalyzerConfig = {
                "CollectionInterval": rs485CollectionIntervalField.text,
                "Current": rs485CurrentField.text,
                "Voltage": rs485VoltageField.text,
                "O2": rs485O2Field.text,
                "Co": rs485CoField.text,
                "Tempature": rs485TempatureField.text,
                "Switch1": rs485Switch1Field.text,
                "BackflowDelayTime": rs485BackflowDelayTimeField.text
            }

            // 收集DCS配置（包含基本配置和OPC配置）
            var newDCSOPCConfig = {
                "CollectionInterval": dcsCollectionIntervalField.text,
                "OPCServerProgID": opcServerProgIDField.text,
                "OPCServerHost": opcServerHostField.text,
                "OPCUpdateRate": opcUpdateRateField.text,
                "OPCGroupName": opcGroupNameField.text,
                "OPCFurnaceSetTempTag": opcFurnaceSetTempTagField.text,
                "OPCFurnaceActualTempTag": opcFurnaceActualTempTagField.text,
                "OPCActualFurnacePressureTag": opcActualFurnacePressureTagField.text,
                "OPCCoalFeedRateTag": opcCoalFeedRateTagField.text,
                "OPCActualRawMaterial1Tag": opcActualRawMaterial1TagField.text,
                "OPCActualRawMaterial2Tag": opcActualRawMaterial2TagField.text,
                "OPCPlannedRawMaterialTag": opcPlannedRawMaterialTagField.text,
                "OPCInducedDraftFanSpeedTag": opcInducedDraftFanSpeedTagField.text,
                "OPCCoalFeedSetTag": opcCoalFeedSetTagField.text,
                "OPCRawMaterial1SetTag": opcRawMaterial1SetTagField.text,
                "OPCRawMaterial2SetTag": opcRawMaterial2SetTagField.text,
                "OPCInducedDraftFanSetTag": opcInducedDraftFanSetTagField.text,
                "OPCCoalFeedSetWriteTag": opcCoalFeedSetWriteTagField.text,
                "OPCRawMaterial1SetWriteTag": opcRawMaterial1SetWriteTagField.text,
                "OPCRawMaterial2SetWriteTag": opcRawMaterial2SetWriteTagField.text,
                "OPCInducedDraftFanSetWriteTag": opcInducedDraftFanSetWriteTagField.text
            }

            // 收集参数调整配置
            var newParameterAdjustmentConfig = {
                "AutoAdjustmentEnabled": autoAdjustmentEnabledSwitch.checked ? "true" : "false",
                "AdjustmentCheckInterval": adjustmentCheckIntervalField.text,
                "RawMaterialDiffThreshold": rawMaterialDiffThresholdField.text,
                "RawMaterialAdjustmentStep": rawMaterialAdjustmentStepField.text,
                "FurnaceTempDiffThreshold": furnaceTempDiffThresholdField.text,
                "FurnaceTempMaxValue": furnaceTempMaxValueField.text,
                "FurnaceTempMinValue": furnaceTempMinValueField.text,
                "CoalFeedAdjustmentStep": coalFeedAdjustmentStepField.text,
                "CoalFeedMaxValue": coalFeedMaxValueField.text,
                "CoalFeedMinValue": coalFeedMinValueField.text,
                "OxygenConcentrationDiffThreshold": oxygenConcentrationDiffThresholdField.text,
                "OxygenConcentrationSetpoint": oxygenConcentrationSetpointField.text,
                "OxygenRawMaterialAdjustmentStep": oxygenRawMaterialAdjustmentStepField.text,
                "OxygenCoalFeedAdjustmentStep": oxygenCoalFeedAdjustmentStepField.text,
                "FurnacePressureDiffThreshold": furnacePressureDiffThresholdField.text,
                "FurnacePressureSetpoint": furnacePressureSetpointField.text,
                "FurnacePressureSetpointMin": furnacePressureSetpointMinField.text,
                "FurnacePressureSetpointMax": furnacePressureSetpointMaxField.text,
                "InducedDraftFanAdjustmentStep": inducedDraftFanAdjustmentStepField.text,
                "InducedDraftFanMaxSpeed": inducedDraftFanMaxSpeedField.text,
                "InducedDraftFanMinSpeed": inducedDraftFanMinSpeedField.text
            }

            // 先设置所有配置到内存中，然后一次性保存
            // 设置RS485通信配置
            for (var key in newRS485Config) {
                configManagerQML.setConfigValue("RS485", key, newRS485Config[key])
            }

            // 获取烟气分析仪设备名称并设置设备配置
            var smokeAnalyzerDeviceName = configManagerQML.getSmokeAnalyzerDeviceName()
            if (smokeAnalyzerDeviceName && smokeAnalyzerDeviceName.length > 0) {
                for (var key in newSmokeAnalyzerConfig) {
                    configManagerQML.setConfigValue(smokeAnalyzerDeviceName, key, newSmokeAnalyzerConfig[key])
                }
            } else {
                for (var key in newSmokeAnalyzerConfig) {
                    configManagerQML.setConfigValue("分解炉1号", key, newSmokeAnalyzerConfig[key])
                }
            }

            // 设置DCS配置（包含基本配置和OPC配置）
            for (var key in newDCSOPCConfig) {
                configManagerQML.setConfigValue("DCS1", key, newDCSOPCConfig[key])
            }

            // 设置参数调整配置
            for (var key in newParameterAdjustmentConfig) {
                configManagerQML.setConfigValue("ParameterAdjustment", key, newParameterAdjustmentConfig[key])
            }

            // 一次性保存所有配置
            var saveSuccess = configManagerQML.saveAllConfigs()

            if (saveSuccess) {
                hasUnsavedChanges = false
                // 合并RS485通信配置和烟气分析仪设备配置
                var combinedRS485Config = {}
                for (var key in newRS485Config) {
                    combinedRS485Config[key] = newRS485Config[key]
                }
                for (var key in newSmokeAnalyzerConfig) {
                    combinedRS485Config[key] = newSmokeAnalyzerConfig[key]
                }
                rs485Config = combinedRS485Config
                dcsOPCConfig = newDCSOPCConfig
                parameterAdjustmentConfig = newParameterAdjustmentConfig

                // 显示配置保存成功，询问是否重启数据采集
                restartDialog.open()
            } else {
                // 显示错误消息
                errorMessage.text = "配置保存失败，请检查输入参数！"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

    // 重启应用程序的函数
    function restartApplication() {
        if (typeof configManagerQML !== 'undefined') {
            // 显示重启进度消息
            successMessage.text = "正在使用重启脚本重启应用程序，请稍候..."
            successMessage.visible = true

            // 调用重启功能
            var success = configManagerQML.restartApplication()

            if (success) {
                successMessage.text = "✅ 重启脚本已启动，应用程序将彻底重启，新的配置将在重启后生效。"
                successMessage.visible = true
                successTimer.start()
            } else {
                errorMessage.text = "❌ 重启脚本启动失败！请手动运行'重启软件.bat'或重启软件。"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

}
