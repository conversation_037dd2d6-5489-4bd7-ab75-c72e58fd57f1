# 将烟气监控从30分钟扩展到60分钟数据采集指南

## 概述

当前系统配置为采集30分钟的数据（600个数据点）。要改为60分钟（1小时），这是一个较大的跨越，需要仔细评估32位程序的内存承受能力。

## 核心参数计算

- **当前配置**: 30分钟 = 1800秒 ÷ 3秒/点 = 600个数据点
- **目标配置**: 60分钟 = 3600秒 ÷ 3秒/点 = 1200个数据点
- **内存增长**: 从600个点增加到1200个点（翻倍！）

## 需要修改的关键位置

### 1. monitoring_datasource.h (第122行)
```cpp
// 当前
static const int MAX_DATA_POINTS = 600; // 30分钟按照3秒采集的频率那就是600个点。

// 修改为
static const int MAX_DATA_POINTS = 1200; // 60分钟按照3秒采集的频率那就是1200个点。
```

### 2. monitoring_datasource.cpp

#### 2.1 getMaxWindowDataPoints函数 (第202行)
```cpp
// 当前
int windowSeconds = 30 * 60;  // 30分钟 = 1800秒

// 修改为
int windowSeconds = 60 * 60;  // 60分钟 = 3600秒
```

#### 2.2 所有注释中的"30分钟"和"600个点"
- 第200行: "计算60分钟窗口需要的最大数据点数"
- 第295行: "固定3秒采集间隔，1200个点=60分钟"
- 第320行: "固定3秒采集间隔，1200个点=60分钟"
- 第383行: "简单逻辑：MAX_DATA_POINTS=1200就是60分钟的数据"
- 第385行: "还没满60分钟，继续增加"
- 第387行: "满了1200个点后，m_dataCount保持1200，实现滑动窗口"
- 第700行: "固定3秒采集间隔，1200个点=60分钟"
- 第703行: "构建数据点列表 - 将最近的数据映射到0-60分钟"
- 第886行: "如果缓冲区已满（达到1200个点），总是使用全量更新来实现滑动窗口"
- 第898行: "对于未满1200个点的情况，使用增量更新"

### 3. monitoring_datasource.h

#### 3.1 函数注释修改
- 第64行: "获取60分钟窗口需要的最大数据点数"
- 第74行: "简化的图表更新方法 - 只支持60分钟视图"

### 4. MonitoringSystem.qml

#### 4.1 关键UI配置修改
```qml
// 第743行 - 缩放标签
property var zoomLabels: ["60分钟"]

// 第765行 - 窗口大小
property double windowSize: 60.0  // 60分钟窗口

// 第767行 - X轴刻度 (建议每10分钟一个刻度)
tickCount: 7  // 0, 10, 20, 30, 40, 50, 60分钟

// 第1393行 - 时间范围显示
text: "时间范围: 60分钟"
```

#### 4.2 所有注释修改
- 第94行和106行: "X轴固定0-60分钟，不需要更新范围"
- 第739行: "属性定义 - 简化为只有60分钟时间区间"
- 第742行: "只保留60分钟区间"
- 第744行: "当前缩放级别索引，默认选中60分钟"
- 第771行: "固定X轴范围 - 始终显示0-60分钟"
- 第773行: "固定60分钟"
- 第872行: "60分钟视图：0.5分钟步长"
- 第949行: "60分钟视图始终使用分钟"
- 第1026行: "初始化时使用全量更新 - 简化为60分钟视图"
- 第1318行: "缩放控制按钮 - 已简化为只有60分钟，注释掉按钮选择区域"
- 第1386行: "简化版本：只显示当前60分钟视图信息"

## ⚠️ 重要性能考虑

### 内存使用分析（关键！）
- **数据点数量**: 1200个点 (每个点包含O2和CO两个double值)
- **基础数据**: 1200 × 2 × 8字节 ≈ 19.2KB
- **QPointF对象**: 1200 × 2 × 16字节 ≈ 38.4KB
- **QtCharts内部缓存**: 估计额外20-30KB
- **总计**: 约80-90KB的额外内存使用

### 32位程序风险评估
- **内存翻倍**: 从30KB增加到80-90KB
- **碎片化风险**: 频繁的内存分配/释放可能导致碎片化
- **GC压力**: Qt的内存管理压力增加

### 渲染性能影响
- **图表点数**: 1200个点开始对QtCharts有一定压力
- **重绘时间**: 全量更新可能需要更多时间
- **滑动窗口**: 60分钟后的滑动更新会更频繁

## 建议的渐进测试方案

### 阶段1: 45分钟测试 (推荐先试这个)
```cpp
static const int MAX_DATA_POINTS = 900; // 45分钟 = 900个点
int windowSeconds = 45 * 60;  // 45分钟 = 2700秒
property double windowSize: 45.0
tickCount: 10  // 0, 5, 10, 15, 20, 25, 30, 35, 40, 45分钟
```

### 阶段2: 如果45分钟稳定，再试60分钟

## X轴刻度优化建议

对于60分钟的时间跨度，有两种刻度方案：

### 方案A: 每10分钟一个刻度 (推荐)
```qml
tickCount: 7  // 显示: 0, 10, 20, 30, 40, 50, 60分钟
```

### 方案B: 每15分钟一个刻度
```qml
tickCount: 5  // 显示: 0, 15, 30, 45, 60分钟
```

## 修改步骤建议

1. **强烈建议先备份整个项目**
2. **考虑先测试45分钟**，确保稳定后再跳到60分钟
3. **按顺序修改**：
   ```
   monitoring_datasource.h → monitoring_datasource.cpp → MonitoringSystem.qml
   ```
4. **重点关注**：
   - MAX_DATA_POINTS: 600 → 1200
   - windowSeconds: 1800 → 3600
   - windowSize: 30.0 → 60.0
   - tickCount: 7 (每10分钟一个刻度)
   - 所有"30分钟"→"60分钟"，"600个点"→"1200个点"

## 测试要点（非常重要！）

### 启动阶段测试
1. **冷启动**: 程序能否正常启动
2. **内存基线**: 记录启动后的内存使用
3. **UI响应**: 界面是否流畅

### 数据采集阶段测试
1. **前60分钟**: 观察内存增长曲线
2. **30分钟节点**: 检查是否有性能下降
3. **45分钟节点**: 关键观察点
4. **接近60分钟**: 监控内存和CPU使用

### 滑动窗口阶段测试
1. **60分钟后**: 滑动窗口是否正常工作
2. **长时间运行**: 建议测试4-6小时
3. **内存泄漏**: 观察内存是否持续增长
4. **UI流畅度**: 是否出现卡顿或延迟

## 性能监控建议

### 监控指标
- **内存使用**: 任务管理器监控
- **CPU使用**: 特别是图表更新时
- **响应时间**: UI操作的响应速度
- **数据更新延迟**: 3秒更新是否准时

### 预警信号
- 内存使用超过预期（>100KB增长）
- UI出现明显卡顿
- 数据更新出现延迟
- 程序响应变慢

## 风险评估与应对

### 高风险因素
- **内存翻倍**: 32位程序的最大风险
- **图表渲染**: 1200个点可能影响性能
- **长时间运行**: 内存碎片化风险

### 应对策略
1. **立即回退方案**: 保留30分钟配置的备份
2. **中间方案**: 45分钟作为折中选择
3. **优化方案**: 考虑数据压缩或采样优化

## 验证清单

- [ ] MAX_DATA_POINTS 改为 1200
- [ ] windowSeconds 改为 60 * 60 (3600秒)
- [ ] windowSize 改为 60.0
- [ ] tickCount 改为 7 (每10分钟)
- [ ] zoomLabels 改为 ["60分钟"]
- [ ] 时间范围显示改为 "时间范围: 60分钟"
- [ ] 所有"30分钟"注释改为"60分钟"
- [ ] 所有"600个点"注释改为"1200个点"
- [ ] 编译成功
- [ ] 启动测试通过
- [ ] 内存使用监控正常
- [ ] 60分钟数据采集测试
- [ ] 滑动窗口功能测试
- [ ] 4-6小时长时间运行测试
- [ ] 性能基准测试通过

## 回退方案

### 立即回退 (如果出现问题)
1. 恢复30分钟配置的备份
2. 重新编译部署

### 渐进回退 (如果性能不理想)
1. 先尝试45分钟配置
2. 或者尝试50分钟配置
3. 找到最佳平衡点

## 后续优化建议

如果60分钟运行成功，可以考虑：
1. **数据压缩**: 对历史数据进行压缩存储
2. **分层显示**: 远期数据降低精度显示
3. **内存优化**: 优化数据结构减少内存占用
4. **更长时间**: 尝试90分钟或120分钟

## 总结

60分钟是一个重要的里程碑，但也是32位程序的一个挑战。建议：
1. **谨慎测试**: 充分测试每个阶段
2. **监控为主**: 重点关注内存和性能指标
3. **准备回退**: 随时准备回到稳定配置
4. **渐进推进**: 考虑先测试45分钟作为中间步骤
