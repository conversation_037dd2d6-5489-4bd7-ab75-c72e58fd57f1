#ifndef BOILER_H
#define BOILER_H

#include <string>
#include <mutex>
#include <thread>
#include "config_manager.h"

//锅炉类
class Boiler
{
public:
    Boiler(ConfigManager *config_manager,std::string boiler_name);
    ~Boiler();

    //加载配置
    int load_config();

    //配置项
    ConfigManager *config_manager;
    //锅炉名
    std::string boiler_name;

    //设备号配置
    std::string desc;
    std::string protocol;
    int collection_interval;  // 采集时间间隔（秒），从配置文件读取
    unsigned short  device_current =60;
    unsigned short  device_voltage = 50;
    unsigned short  device_o2 = 4;
    unsigned short  device_co = 1;
    unsigned short  device_tempature = 40;
    unsigned short  device_switch1 = 70;  // 反吹反馈开关量信号设备地址

    //采集文件句柄
    int fd;

    //锅炉初始化状态标志
    bool is_initialized;

    //启动线程开始采集数据
    void do_data_collect(Bo<PERSON> *);

    //启动采集线程
    void start_data_collect();

    // stop_data_collect函数已移除，使用detach()方式

    //数据读取方法
    int read_data(int fd, unsigned char *buffer, int length);

    //数据信息
    std::mutex rwMutex;
    float co = 0.00,o2 = 0.00,co2 =0.00;
    float current = 0.00, voltage =0.00, temperature = 0.00;
    int switch1 = 0;  // 反吹反馈信号状态（0=停止，1=运行）

    // 数据准备状态标志
    bool data_ready = false;  // 标识是否已完成至少一次完整的数据采集

    // 线程控制变量已移除，使用detach()方式，线程独立运行

private:

};

#endif // BOILER_H
