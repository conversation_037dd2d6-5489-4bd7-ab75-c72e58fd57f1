# 多时间缓冲区算法总结

## 概述

多时间缓冲区是一种用于实时数据监控系统的数据结构设计模式，它允许系统同时维护多个不同时间范围的数据缓存，用户可以快速切换查看不同时间段的历史数据。

## 核心思想

- **同时写入**：每个新数据点同时写入所有时间范围的缓冲区
- **按需读取**：根据用户选择的时间范围，只从对应缓冲区读取数据
- **循环覆盖**：使用循环缓冲区，自动淘汰最老的数据

## 数据结构设计

### 1. 基础数据点结构
```cpp
struct DataPoint {
    double o2;          // 氧气浓度
    double co;          // 一氧化碳浓度  
    double temperature; // 温度
    double voltage;     // 电压
    double current;     // 电流
    int switch1;        // 开关状态
};
```

### 2. 时间范围缓冲区结构
```cpp
struct TimeRangeBuffer {
    QVector<DataPoint> dataBuffer;  // 存储数据的动态数组
    int currentIndex;               // 当前写入位置索引
    int dataCount;                  // 实际数据点数量
    int maxSize;                    // 缓冲区最大容量

    TimeRangeBuffer(int size) : dataBuffer(size), currentIndex(0), dataCount(0), maxSize(size) {}
};
```

### 3. 多缓冲区实例
```cpp
class MonitoringDataSource {
private:
    // 四个不同时间范围的缓冲区
    TimeRangeBuffer m_buffer60Min;   // 1小时：1200个点
    TimeRangeBuffer m_buffer8Hour;   // 8小时：9600个点
    TimeRangeBuffer m_buffer12Hour;  // 12小时：14400个点
    TimeRangeBuffer m_buffer24Hour;  // 24小时：28800个点
};
```

## 核心算法

### 1. 数据写入算法（循环缓冲区）
```cpp
void addDataPointToBuffer(TimeRangeBuffer& buffer, double o2, double co, 
                         double temperature, double voltage, double current, int switch1)
{
    // 步骤1：在当前索引位置写入新数据
    DataPoint &point = buffer.dataBuffer[buffer.currentIndex];
    point.o2 = o2;
    point.co = co;
    point.temperature = temperature;
    point.voltage = voltage;
    point.current = current;
    point.switch1 = switch1;

    // 步骤2：更新索引到下一个位置（循环）
    buffer.currentIndex = (buffer.currentIndex + 1) % buffer.maxSize;

    // 步骤3：更新数据计数
    if (buffer.dataCount < buffer.maxSize) {
        buffer.dataCount++;  // 还没满，继续增加
    }
    // 满了后，dataCount保持maxSize，实现滑动窗口
}
```

### 2. 同步写入所有缓冲区
```cpp
void addDataPoint(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    // 同时向所有时间范围的缓冲区添加数据
    addDataPointToBuffer(m_buffer60Min, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer8Hour, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer12Hour, o2, co, temperature, voltage, current, switch1);
    addDataPointToBuffer(m_buffer24Hour, o2, co, temperature, voltage, current, switch1);
}
```

### 3. 缓冲区选择算法
```cpp
TimeRangeBuffer& getBufferForTimeRange(int timeRangeMinutes)
{
    switch (timeRangeMinutes) {
        case 60:    return m_buffer60Min;    // 1小时
        case 480:   return m_buffer8Hour;    // 8小时
        case 720:   return m_buffer12Hour;   // 12小时
        case 1440:  return m_buffer24Hour;   // 24小时
        default:    return m_buffer60Min;    // 默认1小时
    }
}
```

### 4. 数据读取算法
```cpp
QVariantList getDataFromBuffer(const TimeRangeBuffer& buffer, const QString& dataType, int requestedMinutes)
{
    QVariantList result;
    const double SAMPLE_INTERVAL_MINUTES = 3.0 / 60.0;  // 3秒间隔
    
    // 计算需要的数据点数
    int requestedPoints = qMin(requestedMinutes * 60 / 3, buffer.dataCount);
    
    for (int i = 0; i < requestedPoints; ++i) {
        // 从最老的数据开始读取
        int index = (buffer.currentIndex - requestedPoints + i + buffer.maxSize) % buffer.maxSize;
        const DataPoint &point = buffer.dataBuffer[index];

        double timeMinutes = i * SAMPLE_INTERVAL_MINUTES;
        
        QVariantMap dataPoint;
        dataPoint["x"] = timeMinutes;
        
        if (dataType == "o2") {
            dataPoint["y"] = point.o2;
        } else if (dataType == "co") {
            dataPoint["y"] = point.co;
        }
        
        result.append(dataPoint);
    }
    
    return result;
}
```

## 循环缓冲区工作原理

### 索引循环机制
```
缓冲区大小 = 5，演示索引变化：

初始: [空][空][空][空][空]  currentIndex=0
写入1: [1 ][空][空][空][空]  currentIndex=1
写入2: [1 ][2 ][空][空][空]  currentIndex=2
写入3: [1 ][2 ][3 ][空][空]  currentIndex=3
写入4: [1 ][2 ][3 ][4 ][空]  currentIndex=4
写入5: [1 ][2 ][3 ][4 ][5 ]  currentIndex=0 (回到开头)
写入6: [6 ][2 ][3 ][4 ][5 ]  currentIndex=1 (覆盖最老数据)
```

### 取模运算的作用
```cpp
// 确保索引永远在有效范围内
currentIndex = (currentIndex + 1) % maxSize;

// 示例：maxSize = 1200
// currentIndex = 1199 → 下次 = (1199+1) % 1200 = 0
```

## 内存占用计算

### 单个数据点大小
```cpp
sizeof(DataPoint) = 8+8+8+8+8+4 = 44字节
```

### 各缓冲区内存占用
- **1小时**: 1200点 × 44字节 = 52.8 KB
- **8小时**: 9600点 × 44字节 = 422.4 KB  
- **12小时**: 14400点 × 44字节 = 633.6 KB
- **24小时**: 28800点 × 44字节 = 1.27 MB
- **总计**: 约 2.4 MB

## 优势与特点

### 优势
1. **快速切换**：无需重新加载数据，响应迅速
2. **数据完整性**：每个时间范围都有完整历史数据
3. **内存可控**：固定大小，不会无限增长
4. **高效访问**：O(1)时间复杂度的插入和访问

### 适用场景
- 实时数据监控系统
- 多时间范围数据展示
- 历史数据快速切换需求
- 内存受限的嵌入式系统

## 实现要点

1. **同步写入**：确保所有缓冲区数据一致性
2. **循环索引**：使用取模运算实现循环覆盖
3. **状态维护**：正确维护currentIndex和dataCount
4. **边界处理**：处理缓冲区未满和已满的不同状态

## 扩展思考

- 可以根据需要调整时间范围和缓冲区大小
- 可以添加数据压缩算法减少内存占用
- 可以实现数据持久化到磁盘
- 可以添加数据统计和分析功能
