#ifndef CSVREADER_H
#define CSVREADER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantList>
#include <QVariantMap>
#include <QDateTime>

class CsvReader : public QObject
{
    Q_OBJECT

public:
    explicit CsvReader(QObject *parent = nullptr);

    // 原有的完整数据读取方法（保持兼容性）
    Q_INVOKABLE QVariantList readDataByDate(const QString &boilerName, const QDate &date);
    Q_INVOKABLE QVariantList readDataByDateRange(const QString &boilerName, const QDate &startDate, const QDate &endDate);
    Q_INVOKABLE QStringList getAvailableDates(const QString &boilerName);
    Q_INVOKABLE bool hasDataForDate(const QString &boilerName, const QDate &date);

    // 新增：专门用于图表的高效数据读取方法
    Q_INVOKABLE QVariantList readChartDataForToday(const QString &boilerName);
    Q_INVOKABLE QVariantList readChartDataByDate(const QString &boilerName, const QDate &date);
    Q_INVOKABLE QVariantList readRecentChartData(const QString &boilerName, int maxPoints = 1000);
    Q_INVOKABLE QVariantList readChartDataByTimeRange(const QString &boilerName, const QDateTime &startTime, const QDateTime &endTime);

    // 获取当前CSV文件路径（用于监控文件变化）
    Q_INVOKABLE QString getCurrentCsvFilePath(const QString &boilerName);

private:
    // 原有的私有方法
    QString buildCsvFilePath(const QString &boilerName, const QDate &date);
    QVariantMap parseCsvLine(const QString &line);
    bool isValidCsvFile(const QString &filePath);

    // 新增：专门用于图表的高效解析方法
    struct ChartDataPoint {
        qint64 timestamp;
        double relativeTimeHours;
        double relativeTimeMinutes;
        double o2;
        double co;

        ChartDataPoint() : timestamp(0), relativeTimeHours(0), relativeTimeMinutes(0), o2(0), co(0) {}
    };

    QVariantMap parseChartDataLine(const QString &line, qint64 baseTimestamp = 0);
    QList<ChartDataPoint> parseChartDataOptimized(const QString &filePath, int maxPoints = 0);
    QVariantList convertToQmlFormat(const QList<ChartDataPoint> &dataPoints);
};

#endif // CSVREADER_H
