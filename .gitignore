# Qt项目 .gitignore 文件

# Qt Creator生成的文件
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
*.prl

# Qt编译输出目录
build/
build-*/
debug/
release/
Debug/
Release/

# 项目特定的构建目录
build_32bit_debug/
build_32bit_release/

# 编译生成的对象文件
*.o
*.obj
*.so
*.dylib
*.a
*.lib
*.exp
*.pdb
*.ilk

# 可执行文件
*.exe
*.app
*.dmg
*.deb
*.rpm

# 临时文件
*.tmp
*.temp
*.log
*.bak
*.swp
*.swo
*~

# IDE和编辑器文件
.vscode/
.idea/
*.sublime-*
*.kate-swp
.DS_Store
Thumbs.db

# Windows系统文件
desktop.ini
$RECYCLE.BIN/

# 编译器和构建工具生成的文件
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
*.cmake
CTestTestfile.cmake
_deps/

# Qt特定的缓存和配置文件
.qmake.cache
.qmake.stash
*.qmlproject.qtds
*.qmlproject.user
*.qmlproject.user.*

# 自动生成的MOC文件
moc_predefs.h

# 资源编译文件（如果是自动生成的）
# qrc_*.cpp

# 翻译文件（编译后的）
*.qm

# 调试信息文件
*.pdb
*.idb

# 性能分析文件
*.psess
*.vsp
*.vspx

# 测试结果
TestResults/

# 包管理器文件（如果使用）
vcpkg_installed/
conan.lock
conanbuildinfo.*

# 数据文件（运行时生成的CSV等）
data/*.csv
data/*.txt
data/*.log

# 配置文件的备份
config.ini.bak
*.ini.backup

# 部署相关文件
deploy/
dist/
installer/

# 文档生成文件
doc/html/
doc/latex/
*.pdf

# 其他常见的忽略文件
*.orig
*.rej
.*.swp
.*.swo

# 特定于项目的忽略项
# 如果有特定的临时文件或测试文件，可以在这里添加

# 示例：
# test_data/
# temp_configs/
# *.test
