#include "boiler.h"
#include <thread>
#include <chrono>
#include "csvfile.h"
#include "smoke_analyzer_comm.h"
#ifdef _WIN32
    #include <windows.h>
    #define close _close
#else
    #include <unistd.h>
#endif

bool ready = false;

Boiler::Boiler(ConfigManager *config_manager,std::string boiler_name) {
    this->config_manager = config_manager;
    this->boiler_name = boiler_name;
    this->is_initialized = false;  // 初始化为false
    this->data_ready = false;      // 显式初始化数据准备标志
    debug_printf("调试: 创建锅炉 '%s'\n", boiler_name.c_str());

    if (load_config() == 0) {
        this->is_initialized = true;
        debug_printf("调试: 锅炉 '%s' 配置加载成功\n", boiler_name.c_str());
    } else {
        debug_printf("错误: 锅炉 '%s' 配置加载失败\n", boiler_name.c_str());
    }
}

Boiler::~Boiler(){
    // 不再需要停止线程，因为使用detach()方式，线程独立运行
    // 只需要关闭文件描述符
    if (fd >= 0) {
        close(fd);
        debug_printf("锅炉 %s 析构，关闭文件描述符: %d\n", boiler_name.c_str(), fd);
    }
}

int Boiler::load_config(){
    //读取锅炉号配置
    debug_printf("调试: 加载锅炉 '%s' 的配置\n", boiler_name.c_str());
    protocol = config_manager->get<std::string>(boiler_name.c_str(),"Protocol");

    // 检查配置文件中是否存在CollectionInterval字段
    if (!config_manager->exists(boiler_name.c_str(), "CollectionInterval")) {
        debug_printf("错误: 锅炉 '%s' 配置文件中缺少 CollectionInterval 字段\n", boiler_name.c_str());
        return -1;  // 返回错误
    }
    collection_interval = config_manager->get<int>(boiler_name.c_str(),"CollectionInterval", 0);  // 使用0作为占位符默认值

    // 验证采集间隔的有效性
    if (collection_interval <= 0) {
        debug_printf("错误: 锅炉 '%s' 的采集间隔配置无效: %d秒\n", boiler_name.c_str(), collection_interval);
        return -1;  // 返回错误
    }

    device_current = config_manager->get<int>(boiler_name.c_str(),"Current");
    device_voltage = config_manager->get<int>(boiler_name.c_str(),"Voltage");
    device_tempature = config_manager->get<int>(boiler_name.c_str(),"Tempature");
    device_o2 = config_manager->get<int>(boiler_name.c_str(),"O2");
    device_co = config_manager->get<int>(boiler_name.c_str(),"Co");
    device_switch1 = config_manager->get<int>(boiler_name.c_str(),"Switch1");

    debug_printf("调试: 锅炉 '%s' 配置 - 协议: %s, 采集间隔: %d秒, CO设备: %d, O2设备: %d, 开关量1设备: %d\n",
           boiler_name.c_str(), protocol.c_str(), collection_interval, device_co, device_o2, device_switch1);
    return 0;
}

void Boiler::do_data_collect(Boiler *boiler){
    unsigned char receive_buffer[256];
    int bytes_received;

    // 添加异常处理保护整个线程
    try {
        //打开串口设备
        int fd = boiler->fd;
        if (fd < 0) {
            debug_printf("***锅炉 %s 串口打开失败，文件描述符: %d\n", boiler->boiler_name.c_str(), fd);
            ready = true;
            return;
        }

        debug_printf("锅炉 %s 数据采集线程启动，文件描述符: %d\n", boiler->boiler_name.c_str(), fd);

    // 烟气分析仪不再创建独立的CSV文件，数据将由DCS设备统一管理
    // 持续采集数据，线程独立运行直到程序结束
    int count = 0;
    while (true) {
        //采集数据
        bytes_received = boiler->read_data(fd, receive_buffer, sizeof(receive_buffer));

        // 烟气分析仪数据不再直接写入CSV，而是存储在内存中供DCS设备使用
        // 数据已经通过read_data()函数更新到boiler对象的成员变量中

        count++;
        if (count % 10 == 0) {
            debug_printf("锅炉 %s 已采集 %d 次数据，采集间隔: %d秒\n", boiler->boiler_name.c_str(), count, boiler->collection_interval);
        }

        std::this_thread::sleep_for(std::chrono::seconds(boiler->collection_interval)); // 使用配置的采集间隔
    }

        debug_printf("锅炉 %s 数据采集线程退出\n", boiler->boiler_name.c_str());
        ready = true;

    } catch (const std::exception& e) {
        debug_printf("锅炉 %s 数据采集线程异常: %s\n", boiler->boiler_name.c_str(), e.what());
        ready = true;
    } catch (...) {
        debug_printf("锅炉 %s 数据采集线程发生未知异常\n", boiler->boiler_name.c_str());
        ready = true;
    }
}

void Boiler::start_data_collect(){
    std::thread t(&Boiler::do_data_collect, this, this); // 绑定 this 指针和参数
    debug_printf("%s waiting collect finished...\n", this->boiler_name.c_str());
    t.detach(); // 使用detach方式，与hello项目保持一致
}

// stop_data_collect函数已移除，因为使用detach()方式，线程独立运行直到程序结束




//采集数据
int Boiler::read_data(int fd, unsigned char *buffer, int length) {
    if (fd<0) {
        // 串口未连接，使用锁保护地设置无效数据
        {
            std::lock_guard<std::mutex> lock(rwMutex);
            co = o2 = current = voltage = temperature = 0.0f;
            switch1 = 0;  // 反吹反馈信号设为停止状态
        }
        debug_printf("串口未连接，无法采集数据\n");
        return 0; // 返回0表示没有数据
    }

    // 使用全局串口互斥锁保护整个数据采集过程，解决COM3端口共享问题
    std::lock_guard<std::mutex> serial_lock(g_serial_port_mutex);

    // 在开始采集前清理串口缓冲区，避免DCS数据干扰
    clear_serial_buffer(fd);

    // 临时变量存储读取的数据
    float temp_co = 0.0f, temp_o2 = 0.0f;
    float temp_current = 0.0f, temp_voltage = 0.0f, temp_temperature = 0.0f;
    int temp_switch1 = 0;  // 反吹反馈信号临时变量，默认停止状态
    int count;
    unsigned int registers[10];

    // 读取所有数据（优化时序，减少等待时间，提高采集效率）
    // CO读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_co, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_co = registers[0];  // 直接使用原始值，不乘以0.01
            // 立即更新CO数据
            {
                std::lock_guard<std::mutex> lock(rwMutex);
                co = temp_co;
            }
            break;
        } else if (retry == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(150));

    // O2读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_o2, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_o2 = registers[0]*0.01;
            // 立即更新O2数据
            {
                std::lock_guard<std::mutex> lock(rwMutex);
                o2 = temp_o2;
            }
            break;
        } else if (retry == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(150));



    // 电流读取
    for (int retry = 0; retry < 2; retry++) {
            count = read_holding_registers(fd, device_current, 0x0000, 1, registers,false);
            if (count > 0) {
                temp_current = registers[0]*0.001;
                // 立即更新电流数据
                {
                    std::lock_guard<std::mutex> lock(rwMutex);
                    current = temp_current;
                }
                break;
            } else if (retry == 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(150));

    // 压力表读取（使用压力表报文解析逻辑）
    for (int retry = 0; retry < 2; retry++) {
            count = read_holding_registers(fd, device_voltage, 0x1000, 1, registers,false);
            if (count > 0) {
                // 使用专用的压力表数据解析函数
                // 根据压力表报文解析文档进行数据处理
                uint16_t raw_pressure_data = (uint16_t)registers[0];
                temp_voltage = parse_pressure_data(raw_pressure_data);

                // 立即更新压力数据（仍使用voltage变量名保持兼容性）
                {
                    std::lock_guard<std::mutex> lock(rwMutex);
                    voltage = temp_voltage;
                }
                break;
            } else if (retry == 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(150));

    // 温度读取
    for (int retry = 0; retry < 2; retry++) {
            count = read_holding_registers(fd, device_tempature, 0x0000, 1, registers,false);
            if (count > 0) {
                temp_temperature = registers[0]*0.1;
                // 立即更新温度数据
                {
                    std::lock_guard<std::mutex> lock(rwMutex);
                    temperature = temp_temperature;
                }
                break;
            } else if (retry == 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(150));

    // 反吹反馈开关量信号读取 - 从4096(0x1000)开始读取第一个DI状态
    for (int retry = 0; retry < 2; retry++) {
            count = read_holding_registers(fd, device_switch1, 0x1000, 1, registers, false);
            if (count > 0) {
                // 根据DI/DO状态表：0x0000=无信号，0x0001=有信号
                // 反吹反馈：0=停止，1=运行
                temp_switch1 = (registers[0] == 0x0001) ? 1 : 0;  // 0x0001表示运行，其他表示停止
                // 立即更新开关量信号数据
                {
                    std::lock_guard<std::mutex> lock(rwMutex);
                    switch1 = temp_switch1;
                }
                break;
            } else if (retry == 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

    // 简化的数据采集完成日志（每10次输出一次）
    static int log_counter = 0;
    log_counter++;
    if (log_counter % 10 == 0) {
        std::lock_guard<std::mutex> lock(rwMutex);
        debug_printf("锅炉 %s 数据采集: CO=%.2f, O2=%.2f, Temp=%.2f, Switch1=%d\n",
                    boiler_name.c_str(), co, o2, temperature, switch1);
    }

    // 设置数据准备标志 - 表示已完成一次完整的数据采集
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        if (!data_ready) {
            data_ready = true;
            debug_printf("锅炉 %s 首次数据采集完成\n", boiler_name.c_str());
        }
    }




    //TimeStamp,O2,CO,T,V,I,Switch1
    int data_length = snprintf((char *)buffer, length, "%.2f,%.2f,%.2f,%.2f,%.2f,%d",
                               o2, co, temperature, voltage, current, switch1);
    return data_length;
}


