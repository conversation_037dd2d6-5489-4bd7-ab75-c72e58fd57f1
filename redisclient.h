#ifndef REDISCLIENT_H
#define REDISCLIENT_H

#include <QObject>
#include <QVariantList>
#include <QVariantMap>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>
#include <QDateTime>
#include <QDebug>
#include <QTcpSocket>
#include <QByteArray>
#include <QString>
#include <QStringList>

/**
 * @brief Redis客户端类 - 用于缓存图表数据
 * 
 * 这个类实现了一个简单的Redis客户端，专门用于缓存监控系统的图表数据。
 * 主要功能：
 * - 存储和读取QVariantList格式的图表数据
 * - 自动过期管理
 * - 连接状态监控
 * - 错误处理和重连机制
 */
class RedisClient : public QObject
{
    Q_OBJECT

public:
    explicit RedisClient(QObject *parent = nullptr);
    ~RedisClient();

    // 连接管理
    bool connectToRedis(const QString &host = "127.0.0.1", int port = 6379, int database = 0, const QString &password = "");
    void disconnectFromRedis();
    bool isConnected() const;

    // 数据操作
    bool setChartData(const QString &key, const QVariantList &data, int expireSeconds = 86400);
    QVariantList getChartData(const QString &key);
    bool hasChartData(const QString &key);
    bool deleteKey(const QString &key);
    
    // 批量操作
    QStringList getKeys(const QString &pattern = "*");
    bool deleteKeys(const QStringList &keys);
    void clearExpiredKeys();

    // 配置
    void setDefaultExpireTime(int seconds) { m_defaultExpireSeconds = seconds; }
    int getDefaultExpireTime() const { return m_defaultExpireSeconds; }

    // 统计信息
    qint64 getMemoryUsage();
    int getKeyCount();

signals:
    void connected();
    void disconnected();
    void error(const QString &errorMessage);

private slots:
    void onSocketConnected();
    void onSocketDisconnected();
    void onSocketError(QAbstractSocket::SocketError error);
    void onSocketReadyRead();
    void checkConnection();

private:
    // Redis协议相关
    QString formatRedisCommand(const QStringList &args);
    QVariant parseRedisResponse(const QByteArray &response);
    bool sendCommand(const QStringList &args);
    QVariant sendCommandAndWait(const QStringList &args, int timeoutMs = 5000);
    
    // 数据序列化
    QByteArray serializeVariantList(const QVariantList &data);
    QVariantList deserializeVariantList(const QByteArray &data);
    
    // 连接管理
    void setupConnectionTimer();
    bool selectDatabase(int database);
    bool authenticatePassword(const QString &password);

private:
    QTcpSocket *m_socket;
    QString m_host;
    int m_port;
    int m_database;
    QString m_password;
    bool m_connected;
    int m_defaultExpireSeconds;
    
    // 连接监控
    QTimer *m_connectionTimer;
    QTimer *m_cleanupTimer;
    
    // 响应缓冲
    QByteArray m_responseBuffer;
    bool m_waitingForResponse;
    QVariant m_lastResponse;
    
    // 错误处理
    int m_reconnectAttempts;
    static const int MAX_RECONNECT_ATTEMPTS = 3;
    static const int CONNECTION_CHECK_INTERVAL = 30000; // 30秒
    static const int CLEANUP_INTERVAL = 3600000; // 1小时
};

#endif // REDISCLIENT_H
