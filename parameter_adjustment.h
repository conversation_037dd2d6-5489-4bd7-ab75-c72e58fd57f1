#ifndef PARAMETER_ADJUSTMENT_H
#define PARAMETER_ADJUSTMENT_H

#include <string>
#include <chrono>
#include <mutex>
#include <unordered_map>
#include "config_manager.h"
#include "dcsopc.h"

// 前向声明
class Boiler;

// 参数调整管理器类
class ParameterAdjustmentManager {
public:
    ParameterAdjustmentManager(ConfigManager *config_manager);
    ~ParameterAdjustmentManager();
    
    // 初始化调整管理器
    int initialize();
    
    // 启动自动调整模式
    void enable_auto_adjustment(bool enable);

    // 保存自动调整模式设置到配置文件
    void save_auto_adjustment_setting(bool enable);

    // 检查是否需要调整并执行调整（由DCS采集线程调用）
    void check_and_adjust_parameters(const std::string& dcs_name,
                                   float actual_raw_material,
                                   float planned_raw_material);
    
    // 手动调整模式：计算调整建议但不发送到DCS
    struct AdjustmentSuggestion {
        bool need_adjustment;
        float suggested_raw_material;
        float current_diff;
        std::string reason;
    };

    // 当前调整建议存储结构（供QML页面获取）
    struct CurrentAdjustmentSuggestion {
        bool has_suggestion;                    // 是否有调整建议
        std::string parameter_name;             // 参数名称（如"给煤量"、"实际生料量"、"引风机转速"）
        float suggested_value;                  // 建议值
        float current_value;                    // 当前值
        std::string unit;                       // 单位
        std::string reason;                     // 调整原因
        std::chrono::steady_clock::time_point timestamp; // 建议生成时间
    };

    // 生料量设定调整建议存储结构（同时存储两个生料量设定的建议）
    struct RawMaterialSetSuggestion {
        bool has_suggestion;                    // 是否有生料量设定调整建议
        float raw_material1_set_suggested;     // 生料量1设定建议值
        float raw_material1_set_current;       // 生料量1设定当前值
        float raw_material2_set_suggested;     // 生料量2设定建议值
        float raw_material2_set_current;       // 生料量2设定当前值
        std::string unit;                       // 单位
        std::string reason;                     // 调整原因
        std::chrono::steady_clock::time_point timestamp; // 建议生成时间

        RawMaterialSetSuggestion() : has_suggestion(false),
                                   raw_material1_set_suggested(0.0f), raw_material1_set_current(0.0f),
                                   raw_material2_set_suggested(0.0f), raw_material2_set_current(0.0f) {}
    };

    AdjustmentSuggestion calculate_adjustment_suggestion(float actual_raw_material,
                                                       float planned_raw_material,
                                                       bool using_temp_planned = false);
    

    
    // 获取调整状态
    bool is_auto_adjustment_enabled() const { return auto_adjustment_enabled; }
    std::chrono::steady_clock::time_point get_last_adjustment_time() const { return last_adjustment_time; }

    // 设置用户可调参数
    void set_furnace_pressure_setpoint(float pressure);     // 设置给定炉压
    void set_oxygen_concentration_setpoint(float concentration); // 设置设定氧气浓度

    // 获取用户可调参数
    float get_furnace_pressure_setpoint() const { return furnace_pressure_setpoint; }
    float get_oxygen_concentration_setpoint() const { return oxygen_concentration_setpoint; }

    // 设置和获取临时参数
    void set_temp_furnace_set_temp(float temp);
    void set_temp_planned_raw_material(float material);
    float get_temp_furnace_set_temp() const { return temp_furnace_set_temp; }
    float get_temp_planned_raw_material() const { return temp_planned_raw_material; }

    // 设置和获取生料量设定目标选择
    void set_raw_material_setpoint_target(int target);  // 1=生料量1设定，2=生料量2设定
    int get_raw_material_setpoint_target() const { return raw_material_setpoint_target; }

    // 获取当前调整建议接口
    CurrentAdjustmentSuggestion get_current_adjustment_suggestion() const;

    // 获取生料量设定调整建议接口
    RawMaterialSetSuggestion get_raw_material_set_suggestion() const;

private:
    ConfigManager *config_manager;

    // 调整间隔配置参数
    int adjustment_check_interval;    // 参数调整检查间隔（每N次采集检查一次）

    // 生料量调整配置参数
    float raw_material_diff_threshold;        // 生料量差值阈值（kg）
    float raw_material_adjustment_step;       // 生料量调节步长（kg）

    // 炉膛温度调整配置参数
    float furnace_temp_diff_threshold;        // 炉膛温度差值阈值（℃，±10度）
    float furnace_temp_adjustment_step;       // 炉膛温度调整步长（℃）
    unsigned short furnace_set_temp_write_address;  // 炉膛设定温度DCS写入地址
    float furnace_temp_max_value;             // 炉膛设定温度最大值（℃）
    float furnace_temp_min_value;             // 炉膛设定温度最小值（℃）

    // 给煤量调整配置参数
    float coal_feed_diff_threshold;           // 给煤量差值阈值（kg）
    float coal_feed_adjustment_step;          // 给煤量调节步长（kg，每次加减10公斤）
    float coal_feed_max_value;                // 给煤量最大值（kg）
    float coal_feed_min_value;                // 给煤量最小值（kg）

    // 氧气浓度调整配置参数
    float oxygen_concentration_diff_threshold; // 氧气浓度差值阈值（%，±0.1%）
    float oxygen_concentration_setpoint;       // 设定氧气浓度（%）
    float oxygen_raw_material_adjustment_step; // 氧气浓度调节生料量步长（kg）
    float oxygen_coal_feed_adjustment_step;    // 氧气浓度调节煤量步长（kg）

    // 炉压调整配置参数
    float furnace_pressure_diff_threshold;    // 炉压差值阈值（Pa，±1Pa）
    float furnace_pressure_setpoint;          // 给定炉压（Pa，手工输入，需要密码验证）
    float furnace_pressure_setpoint_min;      // 给定炉压最小值（Pa，-100）
    float furnace_pressure_setpoint_max;      // 给定炉压最大值（Pa，-50）
    float furnace_pressure_min_value;         // 炉压最小值（Pa）
    float furnace_pressure_max_value;         // 炉压最大值（Pa）
    unsigned short furnace_pressure_write_address;  // 炉压设定值DCS写入地址

    // 引风机调整配置参数
    float induced_draft_fan_adjustment_step;  // 引风机转速调节步长（rpm，幅度0.1rpm）
    float induced_draft_fan_max_speed;        // 引风机最大转速（rpm）
    float induced_draft_fan_min_speed;        // 引风机最小转速（rpm）

    // 调整状态
    bool auto_adjustment_enabled;
    std::chrono::steady_clock::time_point last_adjustment_time;

    // 调整间隔计数器
    int adjustment_counter;           // 参数调整间隔计数器

    // 临时参数（用于替代空的OPC标签）
    float temp_furnace_set_temp;                   // 临时炉膛设定温度（℃）
    float temp_planned_raw_material;               // 临时计划生料量（t/h）

    // 生料量设定目标选择
    int raw_material_setpoint_target;              // 生料量设定调整目标：1=生料量1设定，2=生料量2设定

    // 当前调整建议存储（供QML页面获取）
    CurrentAdjustmentSuggestion current_suggestion;

    // 生料量设定调整建议存储（同时存储两个生料量设定的建议）
    RawMaterialSetSuggestion raw_material_set_suggestion;

    // 线程安全
    mutable std::mutex adjustment_mutex;

    // 内部辅助函数
    void store_adjustment_suggestion(const std::string& parameter_name,
                                   float suggested_value,
                                   float current_value,
                                   const std::string& unit,
                                   const std::string& reason);
    void store_adjustment_suggestion_internal(const std::string& parameter_name,
                                            float suggested_value,
                                            float current_value,
                                            const std::string& unit,
                                            const std::string& reason);

    // 存储生料量设定调整建议（同时计算两个生料量设定的建议）
    void store_raw_material_set_suggestion_internal(float raw_material1_set_suggested,
                                                   float raw_material1_set_current,
                                                   float raw_material2_set_suggested,
                                                   float raw_material2_set_current,
                                                   const std::string& unit,
                                                   const std::string& reason);
    void log_adjustment_action(const std::string& dcs_name,
                              float actual_value,
                              float old_planned_value,
                              float new_planned_value,
                              const std::string& reason);

    // 各参数独立调节方法
    void adjust_raw_material_parameter(const std::string& dcs_name,
                                     float actual_raw_material,
                                     float planned_raw_material,
                                     bool using_temp_planned = false,
                                     float actual_raw_material2 = 0.0f);

    void adjust_coal_feed_parameter(const std::string& dcs_name,
                                  float furnace_set_temp,
                                  float furnace_actual_temp,
                                  float coal_feed_set);
    void adjust_oxygen_concentration_parameter(const std::string& dcs_name,
                                             float oxygen_concentration,
                                             float coal_feed_set,
                                             float target_raw_material_set);
    void adjust_induced_draft_fan_parameter(const std::string& dcs_name,
                                          float actual_furnace_pressure,
                                           float induced_draft_fan_set);

    // DCS通信方法
};

// 全局参数调整管理器实例
extern ParameterAdjustmentManager* g_parameter_adjustment_manager;

// C接口函数，供QML调用
extern "C" {
    // 启用/禁用自动调整
    void enable_auto_parameter_adjustment(bool enable);

    // 获取生料量调整建议（手动模式）
    bool get_adjustment_suggestion(const char* dcs_name,
                                 float* suggested_value,
                                 float* current_diff,
                                 char* reason_buffer,
                                 int buffer_size);

    // 获取给煤量调整建议
    bool get_coal_feed_adjustment_suggestion(const char* dcs_name,
                                           float* suggested_value,
                                           float* current_value,
                                           char* reason_buffer,
                                           int buffer_size);



    // 获取引风机频率调整建议
    bool get_induced_draft_fan_adjustment_suggestion(const char* dcs_name,
                                                   float* suggested_adjustment,
                                                   float* current_pressure,
                                                   char* reason_buffer,
                                                   int buffer_size);



    // 获取自动调整状态
    bool is_auto_adjustment_active();

    // 用户可调参数设置接口
    void set_furnace_pressure_setpoint(float pressure);
    void set_oxygen_concentration_setpoint(float concentration);
    float get_furnace_pressure_setpoint();
    float get_oxygen_concentration_setpoint();

    // 临时参数设置接口
    void set_temp_furnace_set_temp(float temp);
    void set_temp_planned_raw_material(float material);
    float get_temp_furnace_set_temp();
    float get_temp_planned_raw_material();

    // 生料量设定目标选择接口
    void set_raw_material_setpoint_target(int target);
    int get_raw_material_setpoint_target();

    // DCS设定值获取接口
    void get_current_dcs_set_values(const char* dcs_name, float* coal_feed_set, float* raw_material1_set, float* raw_material2_set, float* induced_draft_fan_set);
}

#endif // PARAMETER_ADJUSTMENT_H
