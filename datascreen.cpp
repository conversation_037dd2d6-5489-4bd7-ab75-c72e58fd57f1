#include "datascreen.h"

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#ifdef ENABLE_HARDWARE_DATA
#include "boiler.h"
#include "config_manager.h"
#include "dcsopc.h"
#endif

DataScreen::DataScreen(QObject *parent)
    : QObject(parent)
    , m_message("hello world")
    , m_isRunning(false)
    , m_timer(new QTimer(this))
    , m_isDataConnected(false)
    , m_connectionStatus("")
    , m_isBackflowActive(false)
    , m_isDataUpdateSuspended(false)
    , m_backflowDelayTimer(new QTimer(this))
    , m_backflowDelayTime(60)
    , m_suspendedO2Value("0.00%")
    , m_suspendedCOValue("0ppm")
{
    debug_printf("数据大屏: 构造函数开始初始化\n");

    // 初始化5个核心参数为默认值
    // 烟气分析仪数据
    m_oxygenContent = "0.00%";     // 氧浓度
    m_coContent = "0ppm";          // 一氧化碳浓度

    // DCS数据
    m_furnaceActualTemp = "0.0℃";  // 温度
    m_actualFurnacePressure = "0.00Pa"; // 负压
    m_inducedDraftFanSpeed = "0.0rpm"; // 引风机转速

    // 设置定时器
    connect(m_timer, &QTimer::timeout, this, &DataScreen::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 初始化反吹反馈延迟定时器
    m_backflowDelayTimer->setSingleShot(true);
    connect(m_backflowDelayTimer, &QTimer::timeout, this, &DataScreen::resumeO2COUpdates);

    // 加载锅炉列表
    loadBoilerList();

    // 加载DCS设备列表
    loadDcsList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();

    debug_printf("数据大屏: 构造函数初始化完成\n");
}

QString DataScreen::message() const
{
    return m_message;
}

void DataScreen::setMessage(const QString &message)
{
    if (m_message != message) {
        m_message = message;
        emit messageChanged();
    }
}

// 数据大屏核心参数获取方法（仅保留5个关键参数）

// 烟气分析仪数据
QString DataScreen::oxygenContent() const
{
    return m_oxygenContent;
}

QString DataScreen::coContent() const
{
    return m_coContent;
}

bool DataScreen::isRunning() const
{
    return m_isRunning;
}

QStringList DataScreen::boilerList() const
{
    return m_boilerList;
}

QString DataScreen::currentBoiler() const
{
    return m_currentBoiler;
}

bool DataScreen::isDataConnected() const
{
    return m_isDataConnected;
}

QString DataScreen::connectionStatus() const
{
    return m_connectionStatus;
}

int DataScreen::getCurrentCollectionInterval() const
{
    // 获取当前锅炉和DCS的采集间隔，返回较小的值
    int boilerInterval = 15;  // 默认值
    int dcsInterval = 10;     // 默认值

    // 获取锅炉采集间隔
    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            boilerInterval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    // 获取DCS采集间隔
    if (!m_currentDcs.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            dcsInterval = g_config_manager->get<int>(m_currentDcs.toStdString(), "CollectionInterval", 10);
        }
    }

    // 返回较小的采集间隔，确保能及时获取到数据
    int interval = std::min(boilerInterval, dcsInterval);
    debug_printf("数据大屏采集间隔: 锅炉=%d秒, DCS=%d秒, 使用=%d秒\n",
                boilerInterval, dcsInterval, interval);
    return interval;
}

// DCS数据
QString DataScreen::furnaceActualTemp() const
{
    return m_furnaceActualTemp;
}

QString DataScreen::actualFurnacePressure() const
{
    return m_actualFurnacePressure;
}

QString DataScreen::inducedDraftFanSpeed() const
{
    return m_inducedDraftFanSpeed;
}

QStringList DataScreen::dcsList() const
{
    return m_dcsList;
}

QString DataScreen::currentDcs() const
{
    return m_currentDcs;
}

void DataScreen::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void DataScreen::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("数据大屏: 切换锅炉从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        m_currentBoiler = boiler;

        // 数据大屏不需要清理数据缓存

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "";
        emit dataConnectionChanged();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏: 立即检测新设备 '%s' 的连接状态\n", boiler.toStdString().c_str());
            updateData();
        }
    }
}

void DataScreen::setCurrentDcs(const QString &dcs)
{
    if (m_currentDcs != dcs) {
        debug_printf("数据大屏: 切换DCS设备从 '%s' 到 '%s'\n",
                    m_currentDcs.toStdString().c_str(), dcs.toStdString().c_str());

        m_currentDcs = dcs;

        // 更新定时器间隔以匹配新DCS设备的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentDcsChanged();

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "";
        emit dataConnectionChanged();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏: 立即检测新DCS设备 '%s' 的连接状态\n", dcs.toStdString().c_str());
            updateData();
        }
    }
}

void DataScreen::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("数据大屏监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("数据大屏监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("数据大屏监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
            emit dataChanged();  // 强制触发数据变化信号
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("数据大屏监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
            emit dataChanged();  // 强制触发数据变化信号
        });
    }
}

void DataScreen::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();
    }
}

void DataScreen::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("数据大屏更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    // === 设备连接状态检查优化 ===
    // 在执行数据更新之前，先检查设备连接状态
    // 如果任一设备未连接，则跳过数据更新，避免性能浪费

    bool anyDeviceConnected = false;

    // 1. 检查烟气分析仪连接状态
    if (!m_currentBoiler.isEmpty()) {
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        std::string boilerName = m_currentBoiler.toStdString();
        auto boiler_it = boiler_map.find(boilerName);
        if (boiler_it != boiler_map.end() && boiler_it->second && boiler_it->second->fd >= 0) {
            anyDeviceConnected = true;
            debug_printf("数据大屏连接检查: 烟气分析仪 '%s' 已连接 (fd=%d)\n",
                        boilerName.c_str(), boiler_it->second->fd);
        } else {
            debug_printf("数据大屏连接检查: 烟气分析仪 '%s' 未连接\n", boilerName.c_str());
        }
#endif
    }

    // 2. 检查DCS设备连接状态
    if (!m_currentDcs.isEmpty()) {
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;
        std::string dcsName = m_currentDcs.toStdString();
        auto dcs_it = dcs_opc_map.find(dcsName);
        if (dcs_it != dcs_opc_map.end() && dcs_it->second && dcs_it->second->is_opc_connected()) {
            anyDeviceConnected = true;
            debug_printf("数据大屏连接检查: DCS设备 '%s' 已连接\n", dcsName.c_str());
        } else {
            debug_printf("数据大屏连接检查: DCS设备 '%s' 未连接\n", dcsName.c_str());
        }
#endif
    }

    // 3. 如果没有任何设备连接，跳过数据更新
    if (!anyDeviceConnected) {
        debug_printf("数据大屏: 所有设备均未连接，跳过数据更新以节省性能\n");

        // 更新连接状态为未连接
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "设备未连接";
            emit dataConnectionChanged();
        }

        // 设置所有数据为默认值
        m_oxygenContent = "0.00%";
        m_coContent = "0ppm";
        m_furnaceActualTemp = "0.0℃";
        m_actualFurnacePressure = "0.00Pa";
        m_inducedDraftFanSpeed = "0.0rpm";

        // 发射数据变化信号，让UI显示默认值
        emit dataChanged();

        lastUpdateTime = currentTime;
        return;  // 提前退出，避免无效的设备查询
    }

    debug_printf("数据大屏: 检测到设备连接，继续执行数据更新\n");

    updateSmokeData();
    updateDcsData();

    lastUpdateTime = currentTime;
}

void DataScreen::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        debug_printf("数据大屏烟气: 没有选择锅炉，设置默认值\n");
        m_oxygenContent = "0.00%";
        m_coContent = "0ppm";
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f;
    int switch1 = 1;  // 开关量信号，默认关闭状态
    std::string deviceName = m_currentBoiler.toStdString();

    // 直接获取数据，连接状态检查已在updateData()中完成
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end() && it->second && it->second->fd >= 0) {
        // 直接从设备对象获取数据
        Boiler* boiler_device = it->second;
        try {
            // 使用互斥锁保护数据读取，确保数据一致性
            {
                std::lock_guard<std::mutex> lock(boiler_device->rwMutex);
                // 只获取数据大屏需要的2个核心参数
                o2 = boiler_device->o2;
                co = boiler_device->co;
                switch1 = boiler_device->switch1;
            }
            debug_printf("数据大屏烟气数据获取: %s - O2=%.2f, CO=%.2f, Switch1=%d\n",
                        deviceName.c_str(), o2, co, switch1);

            // 在检查反吹状态之前，先保存当前获取到的最新数值（作为潜在的暂停前数值）
            if (!m_isDataUpdateSuspended) {
                // 只在数据更新未暂停时更新潜在的暂停前数值
                m_suspendedO2Value = QString::number(o2, 'f', 2) + "%";
                m_suspendedCOValue = QString::number(co, 'f', 0) + "ppm";
            }

            // 检查反吹反馈状态并处理氧气和一氧化碳数值更新控制
            checkBackflowStatus(switch1);

            // 更新烟气分析仪的核心数据（氧浓度和一氧化碳浓度）
            if (!m_isDataUpdateSuspended) {
                // 正常更新氧气和一氧化碳数值
                m_oxygenContent = QString::number(o2, 'f', 2) + "%";
                m_coContent = QString::number(co, 'f', 0) + "ppm";
                debug_printf("数据大屏烟气分析仪数据更新: O2='%s', CO='%s'\n",
                            m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());
            } else {
                // 数据更新被暂停，使用保存的暂停前数值
                m_oxygenContent = m_suspendedO2Value;
                m_coContent = m_suspendedCOValue;
                debug_printf("数据大屏: 反吹反馈运行中，使用暂停前的数值 - O2='%s', CO='%s'\n",
                            m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());
            }

            // 更新连接状态
            if (!m_isDataConnected) {
                m_isDataConnected = true;
                m_connectionStatus = "";
                debug_printf("数据大屏连接状态更新: ✓ 烟气分析仪已连接\n");
                emit dataConnectionChanged();
            }

        } catch (...) {
            // 如果数据访问出现异常，只在非暂停期间设置默认值
            debug_printf("数据大屏烟气数据获取异常: %s\n", deviceName.c_str());
            if (!m_isDataUpdateSuspended) {
                m_oxygenContent = "0.00%";
                m_coContent = "0ppm";
            } else {
                // 暂停期间保持暂停前的数值
                m_oxygenContent = m_suspendedO2Value;
                m_coContent = m_suspendedCOValue;
                debug_printf("数据大屏: 异常期间保持暂停前数值 - O2='%s', CO='%s'\n",
                            m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());
            }
        }
    } else {
        // 设备未找到或未连接，只在非暂停期间设置默认值
        debug_printf("数据大屏烟气: 设备 '%s' 未连接\n", deviceName.c_str());
        if (!m_isDataUpdateSuspended) {
            m_oxygenContent = "0.00%";
            m_coContent = "0ppm";
        } else {
            // 暂停期间保持暂停前的数值
            m_oxygenContent = m_suspendedO2Value;
            m_coContent = m_suspendedCOValue;
            debug_printf("数据大屏: 未连接期间保持暂停前数值 - O2='%s', CO='%s'\n",
                        m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());
        }
    }
#else
    // 非硬件模式，只在非暂停期间设置默认值
    if (!m_isDataUpdateSuspended) {
        m_oxygenContent = "0.00%";
        m_coContent = "0ppm";
    } else {
        // 暂停期间保持暂停前的数值
        m_oxygenContent = m_suspendedO2Value;
        m_coContent = m_suspendedCOValue;
        debug_printf("数据大屏: 非硬件模式暂停期间保持暂停前数值 - O2='%s', CO='%s'\n",
                    m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());
    }
#endif

    debug_printf("数据大屏烟气: 发射dataChanged信号\n");
    emit dataChanged();
}

void DataScreen::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("数据大屏设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();

    debug_printf("数据大屏加载锅炉列表完成，共 %d 个设备\n", m_boilerList.size());
    for (const QString& boiler : m_boilerList) {
        debug_printf("  - %s\n", boiler.toStdString().c_str());
    }
#endif
}

void DataScreen::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("数据大屏: ⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 优先使用DCS设备的采集间隔，因为数据大屏主要显示DCS数据
    if (!m_currentDcs.isEmpty()) {
        std::string dcsName = m_currentDcs.toStdString();
        debug_printf("数据大屏: 正在为DCS设备 '%s' 更新UI定时器间隔\n", dcsName.c_str());

        // 首先尝试从DCS OPC对象获取采集间隔
        auto dcs_it = dcs_opc_map.find(dcsName);
        if (dcs_it != dcs_opc_map.end() && dcs_it->second != nullptr && dcs_it->second->is_initialized) {
            int collectionInterval = dcs_it->second->collection_interval;
            int uiInterval = collectionInterval * 1000; // 转换为毫秒
            m_timer->setInterval(uiInterval);
            debug_printf("数据大屏: ✓ 从DCS OPC对象获取采集间隔: %d秒，设置UI更新间隔: %d毫秒\n", collectionInterval, uiInterval);
            debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
            return;
        }

        // 如果DCS对象不可用，直接从配置文件读取DCS采集间隔
        if (g_config_manager->exists(dcsName, "CollectionInterval")) {
            int collectionInterval = g_config_manager->get<int>(dcsName, "CollectionInterval");
            if (collectionInterval > 0) {
                int uiInterval = collectionInterval * 1000; // 转换为毫秒
                m_timer->setInterval(uiInterval);
                debug_printf("数据大屏: ✓ 从配置文件获取DCS设备 '%s' 采集间隔: %d秒，设置UI更新间隔: %d毫秒\n",
                            dcsName.c_str(), collectionInterval, uiInterval);
                debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
                return;
            } else {
                debug_printf("数据大屏: ⚠ DCS设备 '%s' 的采集间隔配置无效: %d秒\n", dcsName.c_str(), collectionInterval);
            }
        } else {
            debug_printf("数据大屏: ⚠ DCS设备 '%s' 的配置文件中缺少 CollectionInterval 字段\n", dcsName.c_str());
        }
    }

    // 如果没有DCS设备或DCS配置无效，回退到使用锅炉配置
    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("数据大屏: 回退到锅炉配置，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("数据大屏: ⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
        debug_printf("数据大屏: 回退到锅炉 '%s' 配置更新UI定时器间隔\n", boilerName.c_str());
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒
        m_timer->setInterval(uiInterval);
        debug_printf("数据大屏: ✓ 从锅炉对象获取采集间隔: %d秒，设置UI更新间隔: %d毫秒\n", collectionInterval, uiInterval);
        debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒
            m_timer->setInterval(uiInterval);
            debug_printf("数据大屏: ✓ 从配置文件获取锅炉 '%s' 采集间隔: %d秒，设置UI更新间隔: %d毫秒\n",
                        boilerName.c_str(), collectionInterval, uiInterval);
            debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
            return;
        } else {
            debug_printf("数据大屏: ⚠ 锅炉 '%s' 的采集间隔配置无效: %d秒\n", boilerName.c_str(), collectionInterval);
        }
    } else {
        debug_printf("数据大屏: ⚠ 锅炉 '%s' 的配置文件中缺少 CollectionInterval 字段\n", boilerName.c_str());
    }
#endif

    debug_printf("数据大屏: ⚠ 无法获取有效的采集间隔配置，定时器未设置\n");
}

void DataScreen::loadDcsList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    m_dcsList.clear();

    // 从全局DCS OPC设备映射中获取设备列表
    for (const auto& pair : dcs_opc_map) {
        m_dcsList.append(QString::fromStdString(pair.first));
    }

    // 如果有DCS设备，设置第一个为默认选择
    if (!m_dcsList.isEmpty() && m_currentDcs.isEmpty()) {
        m_currentDcs = m_dcsList.first();
        debug_printf("数据大屏设置默认DCS设备: '%s'\n", m_currentDcs.toStdString().c_str());

        // 更新定时器间隔以匹配默认DCS设备的采集间隔
        updateTimerInterval();

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏默认DCS设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit dcsListChanged();

    debug_printf("数据大屏加载DCS设备列表完成，共 %d 个设备\n", m_dcsList.size());
    for (const QString& dcs : m_dcsList) {
        debug_printf("  - %s\n", dcs.toStdString().c_str());
    }
#endif
}

void DataScreen::updateDcsData()
{
    debug_printf("数据大屏DCS: 开始更新DCS数据\n");

    // 检查是否有选择的DCS设备
    if (m_currentDcs.isEmpty()) {
        debug_printf("数据大屏DCS: 没有选择DCS设备，设置默认值\n");
        // 设置DCS核心参数默认值
        m_furnaceActualTemp = "0.0℃";        // 温度
        m_actualFurnacePressure = "0.00Pa";  // 负压
        m_inducedDraftFanSpeed = "0.0rpm"; // 引风机转速
        return;
    }

    debug_printf("数据大屏DCS: 当前选择的DCS设备: %s\n", m_currentDcs.toStdString().c_str());

    // 从硬件获取真实DCS数据 - 获取3个核心参数
    float furnace_actual_temp = 0.0f, actual_furnace_pressure = 0.0f;
    float induced_draft_fan_speed = 0.0f;
    std::string dcsName = m_currentDcs.toStdString();

    // 直接获取数据，连接状态检查已在updateData()中完成
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    // 检查OPC设备
    debug_printf("数据大屏DCS: 查找OPC设备 '%s' 在dcs_opc_map中\n", dcsName.c_str());
    auto opc_it = dcs_opc_map.find(dcsName);
    if (opc_it == dcs_opc_map.end()) {
        debug_printf("数据大屏DCS: 错误 - 在dcs_opc_map中找不到设备 '%s'\n", dcsName.c_str());
    } else if (!opc_it->second) {
        debug_printf("数据大屏DCS: 错误 - 设备 '%s' 对象为空\n", dcsName.c_str());
    } else if (!opc_it->second->is_opc_connected()) {
        debug_printf("数据大屏DCS: 错误 - 设备 '%s' OPC未连接\n", dcsName.c_str());
    } else {
        debug_printf("数据大屏DCS: ✓ 设备 '%s' 找到且已连接\n", dcsName.c_str());
    }

    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        // 直接从设备对象获取数据
        DCSOPCDevice* dcs_device = opc_it->second;
        try {
            // 使用互斥锁保护数据读取，确保数据一致性
            // 在锁内部再次检查连接状态，避免竞态条件
            {
                std::lock_guard<std::mutex> lock(dcs_device->rwMutex);

                // 在锁保护下再次检查连接状态，确保数据访问安全
                if (dcs_device->is_opc_connected()) {
                    // 获取数据大屏需要的3个核心参数
                    furnace_actual_temp = dcs_device->furnace_actual_temp;
                    actual_furnace_pressure = dcs_device->actual_furnace_pressure;
                    induced_draft_fan_speed = dcs_device->induced_draft_fan_speed;
                } else {
                    // 连接已断开，使用默认值
                    debug_printf("数据大屏DCS: 设备 '%s' 在数据访问时连接已断开\n", dcsName.c_str());
                    furnace_actual_temp = 0.0f;
                    actual_furnace_pressure = 0.0f;
                    induced_draft_fan_speed = 0.0f;
                }
            }
            debug_printf("数据大屏DCS数据获取: %s - 炉膛实际温度=%.2f, 实际炉压=%.2f, 引风机转速=%.2f\n",
                        dcsName.c_str(), furnace_actual_temp, actual_furnace_pressure, induced_draft_fan_speed);

            // 更新DCS核心参数成员变量
            m_furnaceActualTemp = QString::number(furnace_actual_temp, 'f', 1) + "℃";
            m_actualFurnacePressure = QString::number(actual_furnace_pressure, 'f', 2) + "Pa";
            m_inducedDraftFanSpeed = QString::number(induced_draft_fan_speed, 'f', 1) + "rpm";

            debug_printf("数据大屏DCS核心数据更新: 温度='%s', 负压='%s', 引风机转速='%s'\n",
                        m_furnaceActualTemp.toStdString().c_str(),
                        m_actualFurnacePressure.toStdString().c_str(),
                        m_inducedDraftFanSpeed.toStdString().c_str());

        } catch (...) {
            // 如果数据访问出现异常，设置为默认值
            debug_printf("数据大屏DCS数据获取异常: %s\n", dcsName.c_str());
            m_furnaceActualTemp = "0.0℃";
            m_actualFurnacePressure = "0.00Pa";
            m_inducedDraftFanSpeed = "0.0rpm";
        }
    } else {
        // 设备未找到或未连接，设置默认值
        debug_printf("数据大屏DCS: 设备 '%s' 未连接，设置默认值\n", dcsName.c_str());
        m_furnaceActualTemp = "0.0℃";
        m_actualFurnacePressure = "0.00Pa";
        m_inducedDraftFanSpeed = "0.0rpm";
    }
#else
    // 非硬件模式，设置默认值
    m_furnaceActualTemp = "0.0℃";
    m_actualFurnacePressure = "0.00Pa";
    m_inducedDraftFanSpeed = "0.0rpm";
#endif

    debug_printf("数据大屏DCS: 发射dataChanged信号\n");
    emit dataChanged();  // 发射数据变化信号，通知QML界面更新
}

int DataScreen::getBackflowDelayTime() const
{
    // 从配置文件读取反吹反馈延迟时间
    int delayTime = 60;  // 默认60秒

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            delayTime = g_config_manager->get<int>(m_currentBoiler.toStdString(), "BackflowDelayTime", 60);
        }
    }

    return delayTime;
}

void DataScreen::checkBackflowStatus(int switch1)
{
    bool currentBackflowActive = (switch1 == 1);  // 修正：1表示反吹运行，0表示停止

    // 检查反吹反馈状态是否发生变化
    if (currentBackflowActive != m_isBackflowActive) {
        m_isBackflowActive = currentBackflowActive;

        if (m_isBackflowActive) {
            // 反吹反馈开始运行，暂停氧气和一氧化碳数值更新
            suspendO2COUpdates();
            debug_printf("数据大屏: 检测到反吹反馈开始运行，暂停氧气和一氧化碳数值更新\n");
        } else {
            // 反吹反馈停止，启动延迟恢复定时器
            m_backflowDelayTime = getBackflowDelayTime();
            m_backflowDelayTimer->start(m_backflowDelayTime * 1000);  // 转换为毫秒
            debug_printf("数据大屏: 检测到反吹反馈停止，将在%d秒后恢复氧气和一氧化碳数值更新\n", m_backflowDelayTime);
        }
    }
}

void DataScreen::suspendO2COUpdates()
{
    if (!m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = true;

        // 停止延迟恢复定时器（如果正在运行）
        if (m_backflowDelayTimer->isActive()) {
            m_backflowDelayTimer->stop();
        }

        // 暂停前的数值已经在updateSmokeData中保存了
        debug_printf("数据大屏: 氧气和一氧化碳数值更新已暂停，将使用暂停前数值 - O2: %s, CO: %s\n",
                    m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
    }
}

void DataScreen::resumeO2COUpdates()
{
    if (m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = false;
        debug_printf("数据大屏: 延迟%d秒后，氧气和一氧化碳数值更新已恢复\n", m_backflowDelayTime);

        // 立即触发一次数据更新，以显示最新的氧气和一氧化碳数值
        updateData();
    }
}
