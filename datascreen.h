#ifndef DATASCREEN_H
#define DATASCREEN_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QStringList>
#include <QDateTime>

class DataScreen : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString message READ message WRITE setMessage NOTIFY messageChanged)

    // 数据大屏核心参数属性（5个关键参数）
    // 烟气分析仪数据
    Q_PROPERTY(QString oxygenContent READ oxygenContent NOTIFY dataChanged)      // 氧浓度
    Q_PROPERTY(QString coContent READ coContent NOTIFY dataChanged)              // 一氧化碳浓度

    // DCS数据
    Q_PROPERTY(QString furnaceActualTemp READ furnaceActualTemp NOTIFY dataChanged)        // 温度
    Q_PROPERTY(QString actualFurnacePressure READ actualFurnacePressure NOTIFY dataChanged) // 负压
    Q_PROPERTY(QString inducedDraftFanSpeed READ inducedDraftFanSpeed NOTIFY dataChanged) // 引风机转速

    // 注释：数据大屏只需要实时显示，不需要数据列表缓存
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)

    // 硬件数据相关属性
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(QStringList dcsList READ dcsList NOTIFY dcsListChanged)
    Q_PROPERTY(QString currentDcs READ currentDcs WRITE setCurrentDcs NOTIFY currentDcsChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)

public:
    explicit DataScreen(QObject *parent = nullptr);

    QString message() const;
    void setMessage(const QString &message);

    // 数据大屏核心参数获取方法（5个关键参数）
    // 烟气分析仪数据
    QString oxygenContent() const;       // 氧浓度
    QString coContent() const;           // 一氧化碳浓度

    // DCS数据
    QString furnaceActualTemp() const;   // 温度
    QString actualFurnacePressure() const; // 负压
    QString inducedDraftFanSpeed() const; // 引风机转速

    // 注释：数据大屏只需要实时显示，不需要数据列表获取方法
    bool isRunning() const;

    // 硬件数据相关方法
    QStringList boilerList() const;
    QString currentBoiler() const;
    QStringList dcsList() const;
    QString currentDcs() const;
    bool isDataConnected() const;
    QString connectionStatus() const;

    // 获取当前设备的采集间隔（秒）
    int getCurrentCollectionInterval() const;

public slots:
    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);
    void setCurrentDcs(const QString &dcs);
    void startMonitoring();
    void stopMonitoring();

signals:
    void messageChanged();
    void dataChanged();
    void isRunningChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void dcsListChanged();
    void currentDcsChanged();
    void dataConnectionChanged();
    // 注释：移除smokeDataChanged信号，数据大屏不需要数据列表变化通知

private slots:
    void updateData();

private:
    void loadBoilerList();
    void loadDcsList();
    void updateTimerInterval();
    void updateSmokeData(); // 数据更新方法（仅更新实时显示数据）
    void updateDcsData(); // DCS数据更新方法

private:
    QString m_message;

    // 数据大屏核心参数成员变量（5个关键参数）
    // 烟气分析仪数据
    QString m_oxygenContent;       // 氧浓度
    QString m_coContent;           // 一氧化碳浓度

    // DCS数据
    QString m_furnaceActualTemp;   // 温度
    QString m_actualFurnacePressure; // 负压
    QString m_inducedDraftFanSpeed; // 引风机转速

    bool m_isRunning;
    QTimer *m_timer;

    // 硬件数据相关成员变量
    QStringList m_boilerList;
    QString m_currentBoiler;
    QStringList m_dcsList;
    QString m_currentDcs;
    bool m_isDataConnected;
    QString m_connectionStatus;

    // 反吹反馈控制相关
    bool m_isBackflowActive;           // 反吹反馈是否激活
    bool m_isDataUpdateSuspended;      // 氧气和一氧化碳数据更新是否暂停
    QTimer *m_backflowDelayTimer;      // 反吹反馈延迟恢复定时器
    int m_backflowDelayTime;           // 延迟时间（秒）
    QString m_suspendedO2Value;        // 暂停时保存的氧气浓度值
    QString m_suspendedCOValue;        // 暂停时保存的一氧化碳浓度值

    // 反吹反馈控制方法
    void checkBackflowStatus(int switch1);
    void suspendO2COUpdates();
    void resumeO2COUpdates();
    int getBackflowDelayTime() const;

    // 注释：数据大屏不需要数据存储和时间管理变量
};

#endif // DATASCREEN_H
