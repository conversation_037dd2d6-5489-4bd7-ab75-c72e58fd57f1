# 将烟气监控从20分钟扩展到30分钟数据采集指南

## 概述

当前系统配置为采集20分钟的数据（400个数据点）。要改为30分钟，需要进一步扩展数据缓冲区。

## 核心参数计算

- **当前配置**: 20分钟 = 1200秒 ÷ 3秒/点 = 400个数据点
- **目标配置**: 30分钟 = 1800秒 ÷ 3秒/点 = 600个数据点
- **内存增长**: 从400个点增加到600个点（增加50%）

## 需要修改的关键位置

### 1. monitoring_datasource.h (第122行)
```cpp
// 当前
static const int MAX_DATA_POINTS = 400; // 20分钟按照3秒采集的频率那就是400个点。

// 修改为
static const int MAX_DATA_POINTS = 600; // 30分钟按照3秒采集的频率那就是600个点。
```

### 2. monitoring_datasource.cpp

#### 2.1 getMaxWindowDataPoints函数 (第202行)
```cpp
// 当前
int windowSeconds = 20 * 60;  // 20分钟 = 1200秒

// 修改为
int windowSeconds = 30 * 60;  // 30分钟 = 1800秒
```

#### 2.2 所有注释中的"20分钟"和"400个点"
- 第200行: "计算30分钟窗口需要的最大数据点数"
- 第295行: "固定3秒采集间隔，600个点=30分钟"
- 第320行: "固定3秒采集间隔，600个点=30分钟"
- 第383行: "简单逻辑：MAX_DATA_POINTS=600就是30分钟的数据"
- 第385行: "还没满30分钟，继续增加"
- 第387行: "满了600个点后，m_dataCount保持600，实现滑动窗口"
- 第700行: "固定3秒采集间隔，600个点=30分钟"
- 第703行: "构建数据点列表 - 将最近的数据映射到0-30分钟"
- 第886行: "如果缓冲区已满（达到600个点），总是使用全量更新来实现滑动窗口"
- 第898行: "对于未满600个点的情况，使用增量更新"

### 3. monitoring_datasource.h

#### 3.1 函数注释修改
- 第64行: "获取30分钟窗口需要的最大数据点数"
- 第74行: "简化的图表更新方法 - 只支持30分钟视图"

### 4. MonitoringSystem.qml

#### 4.1 关键UI配置修改
```qml
// 第743行 - 缩放标签
property var zoomLabels: ["30分钟"]

// 第765行 - 窗口大小
property double windowSize: 30.0  // 30分钟窗口

// 第767行 - X轴刻度 (建议每5分钟一个刻度)
tickCount: 7  // 0, 5, 10, 15, 20, 25, 30分钟

// 第1393行 - 时间范围显示
text: "时间范围: 30分钟"
```

#### 4.2 所有注释修改
- 第94行和106行: "X轴固定0-30分钟，不需要更新范围"
- 第739行: "属性定义 - 简化为只有30分钟时间区间"
- 第742行: "只保留30分钟区间"
- 第744行: "当前缩放级别索引，默认选中30分钟"
- 第771行: "固定X轴范围 - 始终显示0-30分钟"
- 第773行: "固定30分钟"
- 第872行: "30分钟视图：0.5分钟步长"
- 第949行: "30分钟视图始终使用分钟"
- 第1026行: "初始化时使用全量更新 - 简化为30分钟视图"
- 第1318行: "缩放控制按钮 - 已简化为只有30分钟，注释掉按钮选择区域"
- 第1386行: "简化版本：只显示当前30分钟视图信息"

## 性能考虑

### 内存使用分析
- **数据点数量**: 600个点 (每个点包含O2和CO两个double值)
- **估算内存**: 600 × 2 × 8字节 ≈ 9.6KB (数据本身)
- **加上QPointF对象**: 600 × 2 × 16字节 ≈ 19.2KB
- **总计**: 约30KB的额外内存使用

### 渲染性能
- **图表点数**: 600个点对于QtCharts来说仍然是轻量级的
- **更新频率**: 每3秒一次，不会造成性能问题
- **滑动窗口**: 30分钟后开始滑动，逻辑保持不变

## 修改步骤建议

1. **备份当前代码**
2. **按顺序修改**：
   ```
   monitoring_datasource.h → monitoring_datasource.cpp → MonitoringSystem.qml
   ```
3. **重点关注**：
   - MAX_DATA_POINTS: 400 → 600
   - windowSeconds: 1200 → 1800
   - windowSize: 20.0 → 30.0
   - tickCount: 11 → 7 (每5分钟一个刻度更合适)
   - 所有"20分钟"→"30分钟"，"400个点"→"600个点"

## X轴刻度优化建议

对于30分钟的时间跨度，建议调整刻度：
```qml
tickCount: 7  // 显示: 0, 5, 10, 15, 20, 25, 30分钟
```
这样比每2分钟一个刻度更清晰，避免X轴过于拥挤。

## 测试要点

1. **启动测试**: 程序能否正常启动
2. **内存监控**: 观察内存使用是否稳定
3. **数据采集**: 前30分钟数据是否正常累积
4. **滑动窗口**: 30分钟后是否开始正常滑动
5. **UI响应**: 界面是否保持流畅
6. **长时间运行**: 建议测试2-3小时确保稳定性

## 风险评估

### 低风险
- 数据结构和算法逻辑不变
- 只是扩大缓冲区大小
- QtCharts处理600个点没有问题

### 需要关注
- **32位程序内存限制**: 虽然增加的内存很少，但需要监控
- **长时间运行稳定性**: 建议测试几小时
- **UI流畅度**: 观察是否有卡顿现象

## 验证清单

- [ ] MAX_DATA_POINTS 改为 600
- [ ] windowSeconds 改为 30 * 60 (1800秒)
- [ ] windowSize 改为 30.0
- [ ] tickCount 改为 7
- [ ] zoomLabels 改为 ["30分钟"]
- [ ] 时间范围显示改为 "时间范围: 30分钟"
- [ ] 所有"20分钟"注释改为"30分钟"
- [ ] 所有"400个点"注释改为"600个点"
- [ ] 编译成功
- [ ] 启动测试通过
- [ ] 30分钟数据采集测试
- [ ] 滑动窗口功能测试
- [ ] 长时间运行稳定性测试

## 回退方案

如果30分钟数据导致性能问题或内存不足：
1. 立即回退到20分钟配置
2. 或者考虑中间值：25分钟 (500个数据点)
3. 优化数据结构，减少内存占用

## 下一步探索

如果30分钟运行稳定，可以考虑：
- 45分钟 (900个点)
- 60分钟 (1200个点)
- 但建议每次增加15分钟，逐步测试瓶颈
