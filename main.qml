import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12
import QtQuick.Window 2.12
import QtCharts 2.3

Window {
    id: root
    width: 1920
    height: 1080
    visible: true
    title: "水泥厂分解炉智慧燃烧系统"

    // 处理窗口关闭事件
    onClosing: function(close) {
        close.accepted = false  // 阻止默认关闭行为
        exitConfirmDialog.open()  // 显示退出确认对话框
    }

    // 错误对话框
    Dialog {
        id: errorDialog
        property alias text: errorLabel.text

        anchors.centerIn: parent
        width: 300
        height: 150
        title: "错误"

        Label {
            id: errorLabel
            anchors.centerIn: parent
            wrapMode: Text.WordWrap
            width: parent.width - 40
        }

        standardButtons: Dialog.Ok
    }

    // 退出确认对话框
    Dialog {
        id: exitConfirmDialog
        anchors.centerIn: parent
        width: 480
        height: 280
        modal: true

        // 移除默认标题栏，使用自定义样式
        header: null

        // 自定义背景
        background: Rectangle {
            color: "#ffffff"
            radius: 15
            border.color: "#e0e0e0"
            border.width: 1

            // 渐变背景
            gradient: Gradient {
                GradientStop { position: 0.0; color: "#ffffff" }
                GradientStop { position: 1.0; color: "#f8f9fa" }
            }

            // 添加多层阴影效果
            Rectangle {
                anchors.fill: parent
                anchors.margins: -4
                color: "transparent"
                radius: parent.radius + 4
                border.color: "#00000010"
                border.width: 1
                z: -2
            }

            Rectangle {
                anchors.fill: parent
                anchors.margins: -2
                color: "transparent"
                radius: parent.radius + 2
                border.color: "#00000015"
                border.width: 1
                z: -1
            }
        }

        // 对话框内容
        Item {
            anchors.fill: parent
            anchors.margins: 30

            Column {
                anchors.centerIn: parent
                spacing: 25
                width: parent.width

                // 图标和标题区域
                Row {
                    anchors.horizontalCenter: parent.horizontalCenter
                    spacing: 15

                    // 询问图标
                    Rectangle {
                        width: 52
                        height: 52
                        radius: 26
                        color: "#e3f2fd"
                        border.color: "#2196f3"
                        border.width: 2

                        // 添加渐变背景
                        gradient: Gradient {
                            GradientStop { position: 0.0; color: "#e3f2fd" }
                            GradientStop { position: 1.0; color: "#bbdefb" }
                        }

                        Text {
                            anchors.centerIn: parent
                            text: "❓"
                            font.pixelSize: 26
                            color: "#1976d2"
                        }
                    }

                    Column {
                        anchors.verticalCenter: parent.verticalCenter
                        spacing: 5

                        Text {
                            text: "退出确认"
                            font.pixelSize: 22
                            font.bold: true
                            color: "#2c3e50"
                            font.family: "Microsoft YaHei"
                        }

                        Text {
                            text: "水泥厂分解炉智慧燃烧系统"
                            font.pixelSize: 14
                            color: "#7f8c8d"
                            font.family: "Microsoft YaHei"
                        }
                    }
                }

                // 提示信息
                Text {
                    text: "您希望如何处理当前运行的系统？"
                    font.pixelSize: 16
                    color: "#495057"
                    anchors.horizontalCenter: parent.horizontalCenter
                    wrapMode: Text.WordWrap
                    width: parent.width
                    horizontalAlignment: Text.AlignHCenter
                }

                // 按钮区域
                Row {
                    anchors.horizontalCenter: parent.horizontalCenter
                    spacing: 20

                    // 最小化到托盘按钮
                    Button {
                        text: "最小化到托盘"
                        width: 150
                        height: 50

                        background: Rectangle {
                            color: parent.pressed ? "#1976d2" : (parent.hovered ? "#2196f3" : "#42a5f5")
                            radius: 10

                            // 渐变效果
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: parent.parent.pressed ? "#1565c0" : (parent.parent.hovered ? "#1e88e5" : "#42a5f5") }
                                GradientStop { position: 1.0; color: parent.parent.pressed ? "#0d47a1" : (parent.parent.hovered ? "#1565c0" : "#1976d2") }
                            }

                            // 添加微妙的阴影
                            Rectangle {
                                anchors.fill: parent
                                anchors.topMargin: 2
                                color: "#00000020"
                                radius: parent.radius
                                z: -1
                            }
                        }

                        contentItem: Row {
                            anchors.centerIn: parent
                            spacing: 8

                            Text {
                                text: "📥"
                                font.pixelSize: 16
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }

                            Text {
                                text: "最小化到托盘"
                                font.pixelSize: 14
                                font.bold: true
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        onClicked: {
                            exitConfirmDialog.close()
                            if (systemTray.available) {
                                systemTray.hideToTray()
                            } else {
                                root.hide()
                            }
                        }
                    }

                    // 退出程序按钮
                    Button {
                        text: "退出程序"
                        width: 130
                        height: 50

                        background: Rectangle {
                            color: parent.pressed ? "#c62828" : (parent.hovered ? "#e53935" : "#f44336")
                            radius: 10

                            // 渐变效果
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: parent.parent.pressed ? "#b71c1c" : (parent.parent.hovered ? "#d32f2f" : "#f44336") }
                                GradientStop { position: 1.0; color: parent.parent.pressed ? "#8e0000" : (parent.parent.hovered ? "#b71c1c" : "#c62828") }
                            }

                            // 添加微妙的阴影
                            Rectangle {
                                anchors.fill: parent
                                anchors.topMargin: 2
                                color: "#00000020"
                                radius: parent.radius
                                z: -1
                            }
                        }

                        contentItem: Row {
                            anchors.centerIn: parent
                            spacing: 8

                            Text {
                                text: "🚪"
                                font.pixelSize: 16
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }

                            Text {
                                text: "退出程序"
                                font.pixelSize: 14
                                font.bold: true
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        onClicked: {
                            exitConfirmDialog.close()
                            systemTray.quitApplication()
                        }
                    }


                }
            }
        }
    }

    StackView {
        id: stackView
        anchors.fill: parent
        initialItem: homePage
    }

    // 首页
    Component {
        id: homePage
        HomePage {
            onNavigateToMonitoring: stackView.push(monitorPage)
            onNavigateToDataScreen: stackView.push(dataScreenPage)
            onNavigateToParameterAdjustment: stackView.push(parameterAdjustmentPage)

            onNavigateToCollectionConfig: stackView.push(configPage)
        }
    }

    // 数据监控页面
    Component {
        id: monitorPage
        MonitoringSystem {
            onNavigateBack: {
                // 立即执行页面切换，提供即时反馈
                stackView.pop()

                // 异步执行清理操作，避免阻塞UI
                Qt.callLater(function() {
                    monitorWindow.stopMonitoring()
                })
            }
        }
    }

    // 采集配置页面
    Component {
        id: configPage
        CollectionConfigView {
            onNavigateBack: stackView.pop()
        }
    }

    // 多维智慧燃烧控制系统页面
    Component {
        id: dataScreenPage
        DataScreenView {}
    }

    // 参数调整页面
    Component {
        id: parameterAdjustmentPage
        ParameterAdjustmentView {}
    }


}
